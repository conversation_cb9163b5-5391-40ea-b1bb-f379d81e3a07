'use client'

import { useState } from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { Copy, Download, Eye, Code } from 'lucide-react'
import { APIResponse } from '@/types'

interface ResponseViewerProps {
  response: APIResponse | null
}

export function ResponseViewer({ response }: ResponseViewerProps) {
  const [viewMode, setViewMode] = useState<'formatted' | 'raw'>('formatted')

  if (!response) {
    return (
      <div className="h-full flex items-center justify-center p-8">
        <div className="text-center">
          <div className="w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4">
            <Eye className="w-8 h-8 text-gray-400" />
          </div>
          <h3 className="text-lg font-medium mb-2">No Response Yet</h3>
          <p className="text-muted-foreground">
            Send a request to see the response here
          </p>
        </div>
      </div>
    )
  }

  const getStatusColor = (status: number) => {
    if (status >= 200 && status < 300) return 'bg-green-500'
    if (status >= 300 && status < 400) return 'bg-yellow-500'
    if (status >= 400 && status < 500) return 'bg-orange-500'
    if (status >= 500) return 'bg-red-500'
    return 'bg-gray-500'
  }

  const getStatusText = (status: number) => {
    const statusTexts: Record<number, string> = {
      200: 'OK',
      201: 'Created',
      204: 'No Content',
      400: 'Bad Request',
      401: 'Unauthorized',
      403: 'Forbidden',
      404: 'Not Found',
      500: 'Internal Server Error',
      502: 'Bad Gateway',
      503: 'Service Unavailable'
    }
    return statusTexts[status] || 'Unknown'
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text).then(() => {
      // Could add a toast notification here
    })
  }

  const downloadResponse = () => {
    const blob = new Blob([JSON.stringify(response.body, null, 2)], {
      type: 'application/json'
    })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `response-${Date.now()}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  const formatJSON = (obj: any) => {
    try {
      return JSON.stringify(obj, null, 2)
    } catch {
      return String(obj)
    }
  }

  const formatSize = (bytes: number) => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  return (
    <div className="h-full overflow-auto p-4 space-y-4">
      {/* Response Status */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-sm">Response Status</CardTitle>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => copyToClipboard(formatJSON(response.body))}
              >
                <Copy className="w-4 h-4 mr-2" />
                Copy
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={downloadResponse}
              >
                <Download className="w-4 h-4 mr-2" />
                Download
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <div className={`w-3 h-3 rounded-full ${getStatusColor(response.status)}`} />
              <Badge variant={response.status >= 200 && response.status < 300 ? "default" : "destructive"}>
                {response.status} {getStatusText(response.status)}
              </Badge>
            </div>
            <div className="text-sm text-muted-foreground">
              {response.time}ms
            </div>
            <div className="text-sm text-muted-foreground">
              {formatSize(response.size)}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Response Content */}
      <Tabs defaultValue="body" className="space-y-4">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="body">Response Body</TabsTrigger>
          <TabsTrigger value="headers">Headers ({Object.keys(response.headers).length})</TabsTrigger>
        </TabsList>

        <TabsContent value="body">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="text-sm">Response Body</CardTitle>
                <div className="flex gap-2">
                  <Button
                    variant={viewMode === 'formatted' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setViewMode('formatted')}
                  >
                    <Eye className="w-4 h-4 mr-2" />
                    Formatted
                  </Button>
                  <Button
                    variant={viewMode === 'raw' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setViewMode('raw')}
                  >
                    <Code className="w-4 h-4 mr-2" />
                    Raw
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {response.status === 0 ? (
                <div className="p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded">
                  <h4 className="font-medium text-red-800 dark:text-red-200 mb-2">Network Error</h4>
                  <p className="text-red-600 dark:text-red-300 text-sm">
                    {response.body?.error || 'Failed to connect to the server'}
                  </p>
                </div>
              ) : (
                <pre className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg overflow-auto text-sm font-mono max-h-96">
                  {viewMode === 'formatted' 
                    ? formatJSON(response.body)
                    : typeof response.body === 'string' 
                      ? response.body 
                      : JSON.stringify(response.body)
                  }
                </pre>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="headers">
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">Response Headers</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {Object.entries(response.headers).map(([key, value]) => (
                  <div key={key} className="flex items-center gap-4 py-2 border-b border-gray-100 dark:border-gray-700 last:border-0">
                    <div className="font-medium text-sm min-w-0 flex-1">
                      {key}
                    </div>
                    <div className="text-sm text-muted-foreground font-mono min-w-0 flex-2">
                      {value}
                    </div>
                  </div>
                ))}
                
                {Object.keys(response.headers).length === 0 && (
                  <div className="text-center py-4 text-muted-foreground">
                    No headers received
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
