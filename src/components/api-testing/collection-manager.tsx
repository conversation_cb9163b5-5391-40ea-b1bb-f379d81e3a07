'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  Plus, 
  Folder, 
  FileText, 
  MoreHorizontal, 
  Trash2, 
  Edit,
  Play
} from 'lucide-react'
import { useAPITestingStore } from '@/lib/store'
import { generateId } from '@/lib/utils'

export function CollectionManager() {
  const { 
    requests, 
    currentRequest, 
    setCurrentRequest, 
    addRequest, 
    removeRequest 
  } = useAPITestingStore()

  const [isCreatingCollection, setIsCreatingCollection] = useState(false)
  const [newCollectionName, setNewCollectionName] = useState('')

  const handleCreateCollection = () => {
    if (newCollectionName.trim()) {
      // For now, we'll just create a new request in this "collection"
      // In a full implementation, you'd create actual collections
      setNewCollectionName('')
      setIsCreatingCollection(false)
    }
  }

  const handleSelectRequest = (request: any) => {
    setCurrentRequest(request)
  }

  const handleDeleteRequest = (requestId: string, e: React.MouseEvent) => {
    e.stopPropagation()
    if (confirm('Are you sure you want to delete this request?')) {
      removeRequest(requestId)
      if (currentRequest?.id === requestId) {
        setCurrentRequest(null)
      }
    }
  }

  const getMethodColor = (method: string) => {
    const colors: Record<string, string> = {
      'GET': 'bg-green-500',
      'POST': 'bg-blue-500',
      'PUT': 'bg-yellow-500',
      'DELETE': 'bg-red-500',
      'PATCH': 'bg-purple-500',
      'HEAD': 'bg-gray-500',
      'OPTIONS': 'bg-gray-500'
    }
    return colors[method] || 'bg-gray-500'
  }

  const sampleRequests = [
    {
      id: 'sample-1',
      name: 'Get Posts',
      method: 'GET',
      url: 'https://jsonplaceholder.typicode.com/posts',
      headers: {},
      body: '',
      collection_id: 'samples',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    },
    {
      id: 'sample-2',
      name: 'Create Post',
      method: 'POST',
      url: 'https://jsonplaceholder.typicode.com/posts',
      headers: { 'Content-Type': 'application/json' },
      body: '{\n  "title": "Sample Post",\n  "body": "This is a sample post",\n  "userId": 1\n}',
      collection_id: 'samples',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    },
    {
      id: 'sample-3',
      name: 'N8N Webhook Test',
      method: 'POST',
      url: 'https://your-n8n-instance.com/webhook/test',
      headers: { 'Content-Type': 'application/json' },
      body: '{\n  "message": "Hello from FlowUI",\n  "timestamp": "2024-01-01T00:00:00Z"\n}',
      collection_id: 'n8n',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }
  ]

  const allRequests = [...requests, ...sampleRequests]

  return (
    <div className="p-4 space-y-4">
      <div className="flex items-center justify-between">
        <h2 className="text-lg font-semibold">Collections</h2>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setIsCreatingCollection(true)}
        >
          <Plus className="w-4 h-4" />
        </Button>
      </div>

      {/* Sample Collections */}
      <div className="space-y-2">
        <Card>
          <CardHeader className="pb-2">
            <div className="flex items-center gap-2">
              <Folder className="w-4 h-4 text-blue-500" />
              <CardTitle className="text-sm">Sample Requests</CardTitle>
              <Badge variant="secondary" className="text-xs">3</Badge>
            </div>
          </CardHeader>
          <CardContent className="pt-0 space-y-1">
            {sampleRequests.filter(req => req.collection_id === 'samples').map((request) => (
              <div
                key={request.id}
                onClick={() => handleSelectRequest(request)}
                className={`
                  flex items-center gap-2 p-2 rounded cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700
                  ${currentRequest?.id === request.id ? 'bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800' : ''}
                `}
              >
                <div className={`w-2 h-2 rounded-full ${getMethodColor(request.method)}`} />
                <FileText className="w-3 h-3 text-gray-400" />
                <span className="text-sm flex-1 truncate">{request.name}</span>
                <Badge variant="outline" className="text-xs">
                  {request.method}
                </Badge>
              </div>
            ))}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <div className="flex items-center gap-2">
              <Folder className="w-4 h-4 text-green-500" />
              <CardTitle className="text-sm">N8N Webhooks</CardTitle>
              <Badge variant="secondary" className="text-xs">1</Badge>
            </div>
          </CardHeader>
          <CardContent className="pt-0 space-y-1">
            {sampleRequests.filter(req => req.collection_id === 'n8n').map((request) => (
              <div
                key={request.id}
                onClick={() => handleSelectRequest(request)}
                className={`
                  flex items-center gap-2 p-2 rounded cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700
                  ${currentRequest?.id === request.id ? 'bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800' : ''}
                `}
              >
                <div className={`w-2 h-2 rounded-full ${getMethodColor(request.method)}`} />
                <FileText className="w-3 h-3 text-gray-400" />
                <span className="text-sm flex-1 truncate">{request.name}</span>
                <Badge variant="outline" className="text-xs">
                  {request.method}
                </Badge>
              </div>
            ))}
          </CardContent>
        </Card>

        {/* User's Custom Requests */}
        {requests.length > 0 && (
          <Card>
            <CardHeader className="pb-2">
              <div className="flex items-center gap-2">
                <Folder className="w-4 h-4 text-purple-500" />
                <CardTitle className="text-sm">My Requests</CardTitle>
                <Badge variant="secondary" className="text-xs">{requests.length}</Badge>
              </div>
            </CardHeader>
            <CardContent className="pt-0 space-y-1">
              {requests.map((request) => (
                <div
                  key={request.id}
                  onClick={() => handleSelectRequest(request)}
                  className={`
                    flex items-center gap-2 p-2 rounded cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 group
                    ${currentRequest?.id === request.id ? 'bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800' : ''}
                  `}
                >
                  <div className={`w-2 h-2 rounded-full ${getMethodColor(request.method)}`} />
                  <FileText className="w-3 h-3 text-gray-400" />
                  <span className="text-sm flex-1 truncate">{request.name}</span>
                  <Badge variant="outline" className="text-xs">
                    {request.method}
                  </Badge>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="opacity-0 group-hover:opacity-100 h-6 w-6 p-0"
                    onClick={(e) => handleDeleteRequest(request.id, e)}
                  >
                    <Trash2 className="w-3 h-3" />
                  </Button>
                </div>
              ))}
            </CardContent>
          </Card>
        )}
      </div>

      {/* Create Collection Form */}
      {isCreatingCollection && (
        <Card>
          <CardContent className="pt-4">
            <div className="space-y-2">
              <Input
                value={newCollectionName}
                onChange={(e) => setNewCollectionName(e.target.value)}
                placeholder="Collection name"
                autoFocus
              />
              <div className="flex gap-2">
                <Button
                  size="sm"
                  onClick={handleCreateCollection}
                  disabled={!newCollectionName.trim()}
                >
                  Create
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    setIsCreatingCollection(false)
                    setNewCollectionName('')
                  }}
                >
                  Cancel
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">Quick Actions</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2">
          <Button
            variant="outline"
            size="sm"
            className="w-full justify-start"
            onClick={() => handleSelectRequest(sampleRequests[0])}
          >
            <Play className="w-4 h-4 mr-2" />
            Try Sample GET
          </Button>
          <Button
            variant="outline"
            size="sm"
            className="w-full justify-start"
            onClick={() => handleSelectRequest(sampleRequests[1])}
          >
            <Play className="w-4 h-4 mr-2" />
            Try Sample POST
          </Button>
          <Button
            variant="outline"
            size="sm"
            className="w-full justify-start"
            onClick={() => handleSelectRequest(sampleRequests[2])}
          >
            <Play className="w-4 h-4 mr-2" />
            Test N8N Webhook
          </Button>
        </CardContent>
      </Card>
    </div>
  )
}
