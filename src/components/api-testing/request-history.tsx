'use client'

import { useState } from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { 
  Clock, 
  Search, 
  Filter, 
  Trash2, 
  RotateCcw,
  CheckCircle,
  XCircle,
  AlertCircle
} from 'lucide-react'

interface HistoryItem {
  id: string
  method: string
  url: string
  status: number
  duration: number
  timestamp: Date
  success: boolean
}

export function RequestHistory() {
  const [searchTerm, setSearchTerm] = useState('')
  const [filterStatus, setFilterStatus] = useState<'all' | 'success' | 'error'>('all')

  // Mock history data - in a real app, this would come from the store
  const historyItems: HistoryItem[] = [
    {
      id: '1',
      method: 'GET',
      url: 'https://jsonplaceholder.typicode.com/posts',
      status: 200,
      duration: 245,
      timestamp: new Date(Date.now() - 1000 * 60 * 5), // 5 minutes ago
      success: true
    },
    {
      id: '2',
      method: 'POST',
      url: 'https://jsonplaceholder.typicode.com/posts',
      status: 201,
      duration: 312,
      timestamp: new Date(Date.now() - 1000 * 60 * 15), // 15 minutes ago
      success: true
    },
    {
      id: '3',
      method: 'GET',
      url: 'https://api.example.com/users',
      status: 404,
      duration: 156,
      timestamp: new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago
      success: false
    },
    {
      id: '4',
      method: 'POST',
      url: 'https://your-n8n-instance.com/webhook/test',
      status: 0,
      duration: 5000,
      timestamp: new Date(Date.now() - 1000 * 60 * 60), // 1 hour ago
      success: false
    }
  ]

  const getStatusIcon = (status: number, success: boolean) => {
    if (!success || status === 0) {
      return <XCircle className="w-4 h-4 text-red-500" />
    }
    if (status >= 200 && status < 300) {
      return <CheckCircle className="w-4 h-4 text-green-500" />
    }
    if (status >= 300 && status < 400) {
      return <AlertCircle className="w-4 h-4 text-yellow-500" />
    }
    return <XCircle className="w-4 h-4 text-red-500" />
  }

  const getMethodColor = (method: string) => {
    const colors: Record<string, string> = {
      'GET': 'bg-green-500',
      'POST': 'bg-blue-500',
      'PUT': 'bg-yellow-500',
      'DELETE': 'bg-red-500',
      'PATCH': 'bg-purple-500'
    }
    return colors[method] || 'bg-gray-500'
  }

  const formatTimestamp = (timestamp: Date) => {
    const now = new Date()
    const diff = now.getTime() - timestamp.getTime()
    const minutes = Math.floor(diff / (1000 * 60))
    const hours = Math.floor(diff / (1000 * 60 * 60))
    const days = Math.floor(diff / (1000 * 60 * 60 * 24))

    if (minutes < 1) return 'Just now'
    if (minutes < 60) return `${minutes}m ago`
    if (hours < 24) return `${hours}h ago`
    return `${days}d ago`
  }

  const filteredHistory = historyItems.filter(item => {
    const matchesSearch = item.url.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         item.method.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesFilter = filterStatus === 'all' ||
                         (filterStatus === 'success' && item.success) ||
                         (filterStatus === 'error' && !item.success)
    
    return matchesSearch && matchesFilter
  })

  const handleRerunRequest = (item: HistoryItem) => {
    // In a real implementation, this would recreate the request and run it
    console.log('Rerunning request:', item)
  }

  const clearHistory = () => {
    if (confirm('Are you sure you want to clear all history?')) {
      // In a real implementation, this would clear the history from the store
      console.log('Clearing history')
    }
  }

  return (
    <div className="h-full overflow-auto p-4 space-y-4">
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-sm flex items-center gap-2">
              <Clock className="w-4 h-4" />
              Request History
            </CardTitle>
            <Button
              variant="ghost"
              size="sm"
              onClick={clearHistory}
              className="text-red-600 hover:text-red-700"
            >
              <Trash2 className="w-4 h-4 mr-2" />
              Clear All
            </Button>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Search and Filter */}
          <div className="flex gap-2">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <Input
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="Search requests..."
                className="pl-10"
              />
            </div>
            <div className="flex gap-1">
              <Button
                variant={filterStatus === 'all' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setFilterStatus('all')}
              >
                All
              </Button>
              <Button
                variant={filterStatus === 'success' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setFilterStatus('success')}
              >
                Success
              </Button>
              <Button
                variant={filterStatus === 'error' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setFilterStatus('error')}
              >
                Error
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* History Items */}
      <div className="space-y-2">
        {filteredHistory.length === 0 ? (
          <Card>
            <CardContent className="py-8">
              <div className="text-center">
                <Clock className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium mb-2">No History Found</h3>
                <p className="text-muted-foreground">
                  {searchTerm || filterStatus !== 'all' 
                    ? 'No requests match your search criteria'
                    : 'Send some requests to see them here'
                  }
                </p>
              </div>
            </CardContent>
          </Card>
        ) : (
          filteredHistory.map((item) => (
            <Card key={item.id} className="hover:shadow-md transition-shadow">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3 flex-1 min-w-0">
                    {getStatusIcon(item.status, item.success)}
                    
                    <div className="flex items-center gap-2">
                      <div className={`w-2 h-2 rounded-full ${getMethodColor(item.method)}`} />
                      <Badge variant="outline" className="text-xs">
                        {item.method}
                      </Badge>
                    </div>
                    
                    <div className="flex-1 min-w-0">
                      <div className="text-sm font-medium truncate">
                        {item.url}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {formatTimestamp(item.timestamp)} • {item.duration}ms
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <Badge 
                      variant={item.success ? "default" : "destructive"}
                      className="text-xs"
                    >
                      {item.status === 0 ? 'Network Error' : item.status}
                    </Badge>
                    
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleRerunRequest(item)}
                      className="h-8 w-8 p-0"
                    >
                      <RotateCcw className="w-3 h-3" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>

      {/* Stats */}
      {filteredHistory.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-sm">Statistics</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-3 gap-4 text-center">
              <div>
                <div className="text-2xl font-bold text-green-600">
                  {filteredHistory.filter(item => item.success).length}
                </div>
                <div className="text-xs text-muted-foreground">Successful</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-red-600">
                  {filteredHistory.filter(item => !item.success).length}
                </div>
                <div className="text-xs text-muted-foreground">Failed</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-blue-600">
                  {Math.round(filteredHistory.reduce((acc, item) => acc + item.duration, 0) / filteredHistory.length)}ms
                </div>
                <div className="text-xs text-muted-foreground">Avg Time</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
