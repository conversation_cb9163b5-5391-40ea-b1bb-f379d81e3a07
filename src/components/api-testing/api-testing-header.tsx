'use client'

import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { useRouter } from 'next/navigation'
import { 
  ArrowLeft,
  Plus,
  Save,
  Send,
  Loader2
} from 'lucide-react'
import { APIRequest } from '@/types'

interface APITestingHeaderProps {
  onNewRequest: () => void
  onSaveRequest: () => void
  onSendRequest: () => void
  isLoading: boolean
  currentRequest: APIRequest | null
}

export function APITestingHeader({
  onNewRequest,
  onSaveRequest,
  onSendRequest,
  isLoading,
  currentRequest
}: APITestingHeaderProps) {
  const router = useRouter()

  const handleBackToDashboard = () => {
    router.push('/dashboard')
  }

  return (
    <header className="h-16 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between px-4">
      {/* Left Section */}
      <div className="flex items-center gap-4">
        <Button
          variant="ghost"
          size="sm"
          onClick={handleBackToDashboard}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="w-4 h-4" />
          Back
        </Button>
        
        <div className="flex items-center gap-2">
          <div className="w-6 h-6 bg-gradient-to-r from-green-600 to-blue-600 rounded"></div>
          <span className="font-semibold">API Testing</span>
        </div>

        {currentRequest && (
          <div className="text-sm text-muted-foreground">
            {currentRequest.name}
          </div>
        )}
      </div>

      {/* Center Section - Quick Actions */}
      <div className="flex items-center gap-2">
        <Button
          variant="ghost"
          size="sm"
          onClick={onNewRequest}
          className="flex items-center gap-2"
        >
          <Plus className="w-4 h-4" />
          New
        </Button>

        {currentRequest && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onSaveRequest}
            className="flex items-center gap-2"
          >
            <Save className="w-4 h-4" />
            Save
          </Button>
        )}
      </div>

      {/* Right Section */}
      <div className="flex items-center gap-2">
        {currentRequest && (
          <>
            <Badge variant="secondary" className="text-xs">
              {currentRequest.method}
            </Badge>
            
            <Button
              onClick={onSendRequest}
              disabled={isLoading || !currentRequest.url}
              className="flex items-center gap-2"
            >
              {isLoading ? (
                <>
                  <Loader2 className="w-4 h-4 animate-spin" />
                  Sending...
                </>
              ) : (
                <>
                  <Send className="w-4 h-4" />
                  Send
                </>
              )}
            </Button>
          </>
        )}
      </div>
    </header>
  )
}
