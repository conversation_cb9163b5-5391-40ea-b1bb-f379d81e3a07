'use client'

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { APITestingHeader } from './api-testing-header'
import { RequestBuilder } from './request-builder'
import { ResponseViewer } from './response-viewer'
import { RequestHistory } from './request-history'
import { CollectionManager } from './collection-manager'
import { useAPITestingStore } from '@/lib/store'
import { HTTPMethod, APIRequest, APIResponse } from '@/types'
import { generateId } from '@/lib/utils'

export function APITestingSuite() {
  const {
    currentRequest,
    setCurrentRequest,
    addRequest,
    updateRequest,
    setLoading,
    setLastResponse,
    isLoading,
    lastResponse
  } = useAPITestingStore()

  const [activeTab, setActiveTab] = useState('builder')

  const handleSendRequest = async () => {
    if (!currentRequest) return

    setLoading(true)
    const startTime = Date.now()

    try {
      const requestOptions: RequestInit = {
        method: currentRequest.method,
        headers: {
          'Content-Type': 'application/json',
          ...currentRequest.headers,
        },
      }

      // Add body for methods that support it
      if (['POST', 'PUT', 'PATCH'].includes(currentRequest.method) && currentRequest.body) {
        requestOptions.body = currentRequest.body
      }

      const response = await fetch(currentRequest.url, requestOptions)
      const duration = Date.now() - startTime
      
      let responseBody
      const contentType = response.headers.get('content-type')
      
      if (contentType?.includes('application/json')) {
        responseBody = await response.json()
      } else {
        responseBody = await response.text()
      }

      const apiResponse: APIResponse = {
        status: response.status,
        headers: Object.fromEntries(response.headers.entries()),
        body: responseBody,
        size: JSON.stringify(responseBody).length,
        time: duration
      }

      setLastResponse(apiResponse)
      setActiveTab('response')
    } catch (error) {
      const duration = Date.now() - startTime
      const errorResponse: APIResponse = {
        status: 0,
        headers: {},
        body: {
          error: error instanceof Error ? error.message : 'Unknown error occurred',
          type: 'network_error'
        },
        size: 0,
        time: duration
      }
      setLastResponse(errorResponse)
      setActiveTab('response')
    } finally {
      setLoading(false)
    }
  }

  const handleNewRequest = () => {
    const newRequest: APIRequest = {
      id: generateId(),
      collection_id: 'default',
      name: 'New Request',
      method: 'GET',
      url: '',
      headers: {},
      body: '',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }
    
    setCurrentRequest(newRequest)
    addRequest(newRequest)
  }

  const handleSaveRequest = () => {
    if (!currentRequest) return
    
    const updatedRequest = {
      ...currentRequest,
      updated_at: new Date().toISOString()
    }
    
    updateRequest(currentRequest.id, updatedRequest)
    setCurrentRequest(updatedRequest)
  }

  return (
    <div className="h-screen bg-gray-50 dark:bg-gray-900 flex flex-col">
      <APITestingHeader 
        onNewRequest={handleNewRequest}
        onSaveRequest={handleSaveRequest}
        onSendRequest={handleSendRequest}
        isLoading={isLoading}
        currentRequest={currentRequest}
      />
      
      <div className="flex-1 flex overflow-hidden">
        {/* Sidebar - Collections */}
        <div className="w-80 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 overflow-y-auto">
          <CollectionManager />
        </div>

        {/* Main Content */}
        <div className="flex-1 flex flex-col">
          {currentRequest ? (
            <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col">
              <div className="border-b border-gray-200 dark:border-gray-700 px-4">
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="builder">Request</TabsTrigger>
                  <TabsTrigger value="response" disabled={!lastResponse}>
                    Response
                    {lastResponse && (
                      <Badge 
                        variant={lastResponse.status >= 200 && lastResponse.status < 300 ? "default" : "destructive"}
                        className="ml-2"
                      >
                        {lastResponse.status}
                      </Badge>
                    )}
                  </TabsTrigger>
                  <TabsTrigger value="history">History</TabsTrigger>
                </TabsList>
              </div>

              <div className="flex-1 overflow-hidden">
                <TabsContent value="builder" className="h-full m-0">
                  <RequestBuilder 
                    request={currentRequest}
                    onUpdateRequest={(updates) => {
                      const updatedRequest = { ...currentRequest, ...updates }
                      setCurrentRequest(updatedRequest)
                    }}
                  />
                </TabsContent>

                <TabsContent value="response" className="h-full m-0">
                  <ResponseViewer response={lastResponse} />
                </TabsContent>

                <TabsContent value="history" className="h-full m-0">
                  <RequestHistory />
                </TabsContent>
              </div>
            </Tabs>
          ) : (
            <div className="flex-1 flex items-center justify-center">
              <Card className="p-8 max-w-md">
                <CardHeader>
                  <CardTitle>Welcome to API Testing</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground mb-4">
                    Create your first API request to get started testing your N8N webhooks and endpoints.
                  </p>
                  <Button onClick={handleNewRequest} className="w-full">
                    Create New Request
                  </Button>
                </CardContent>
              </Card>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
