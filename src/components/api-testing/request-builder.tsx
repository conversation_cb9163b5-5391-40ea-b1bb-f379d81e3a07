'use client'

import { useState } from 'react'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { Plus, X, Code, FileText } from 'lucide-react'
import { APIRequest, HTTPMethod } from '@/types'

interface RequestBuilderProps {
  request: APIRequest
  onUpdateRequest: (updates: Partial<APIRequest>) => void
}

export function RequestBuilder({ request, onUpdateRequest }: RequestBuilderProps) {
  const [newHeader<PERSON>ey, setNewHeaderKey] = useState('')
  const [newHeaderValue, setNewHeaderValue] = useState('')

  const handleMethodChange = (method: HTTPMethod) => {
    onUpdateRequest({ method })
  }

  const handleUrlChange = (url: string) => {
    onUpdateRequest({ url })
  }

  const handleNameChange = (name: string) => {
    onUpdateRequest({ name })
  }

  const handleAddHeader = () => {
    if (newHeaderKey && newHeaderValue) {
      onUpdateRequest({
        headers: {
          ...request.headers,
          [newHeaderKey]: newHeaderValue
        }
      })
      setNewHeaderKey('')
      setNewHeaderValue('')
    }
  }

  const handleRemoveHeader = (key: string) => {
    const newHeaders = { ...request.headers }
    delete newHeaders[key]
    onUpdateRequest({ headers: newHeaders })
  }

  const handleHeaderValueChange = (key: string, value: string) => {
    onUpdateRequest({
      headers: {
        ...request.headers,
        [key]: value
      }
    })
  }

  const handleBodyChange = (body: string) => {
    onUpdateRequest({ body })
  }

  const formatJSON = () => {
    try {
      const formatted = JSON.stringify(JSON.parse(request.body || ''), null, 2)
      onUpdateRequest({ body: formatted })
    } catch (error) {
      // Invalid JSON, do nothing
    }
  }

  const addCommonHeaders = (headerType: string) => {
    const commonHeaders: Record<string, Record<string, string>> = {
      'json': { 'Content-Type': 'application/json' },
      'form': { 'Content-Type': 'application/x-www-form-urlencoded' },
      'auth': { 'Authorization': 'Bearer YOUR_TOKEN_HERE' }
    }

    if (commonHeaders[headerType]) {
      onUpdateRequest({
        headers: {
          ...request.headers,
          ...commonHeaders[headerType]
        }
      })
    }
  }

  return (
    <div className="h-full overflow-auto p-4 space-y-4">
      {/* Request Name */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">Request Details</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="request-name">Request Name</Label>
            <Input
              id="request-name"
              value={request.name}
              onChange={(e) => handleNameChange(e.target.value)}
              placeholder="My API Request"
            />
          </div>
        </CardContent>
      </Card>

      {/* URL and Method */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">Request URL</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2">
            <Select value={request.method} onValueChange={handleMethodChange}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="GET">GET</SelectItem>
                <SelectItem value="POST">POST</SelectItem>
                <SelectItem value="PUT">PUT</SelectItem>
                <SelectItem value="DELETE">DELETE</SelectItem>
                <SelectItem value="PATCH">PATCH</SelectItem>
                <SelectItem value="HEAD">HEAD</SelectItem>
                <SelectItem value="OPTIONS">OPTIONS</SelectItem>
              </SelectContent>
            </Select>
            
            <Input
              value={request.url}
              onChange={(e) => handleUrlChange(e.target.value)}
              placeholder="https://api.example.com/endpoint"
              className="flex-1"
            />
          </div>
          
          {/* Quick URL Templates */}
          <div className="flex gap-2 flex-wrap">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleUrlChange('https://jsonplaceholder.typicode.com/posts')}
            >
              JSONPlaceholder
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleUrlChange('https://httpbin.org/get')}
            >
              HTTPBin
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleUrlChange('https://your-n8n-instance.com/webhook/your-webhook-id')}
            >
              N8N Webhook
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Headers and Body */}
      <Tabs defaultValue="headers" className="space-y-4">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="headers">Headers</TabsTrigger>
          <TabsTrigger value="body" disabled={!['POST', 'PUT', 'PATCH'].includes(request.method)}>
            Body
          </TabsTrigger>
          <TabsTrigger value="auth">Auth</TabsTrigger>
        </TabsList>

        <TabsContent value="headers">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="text-sm">Headers</CardTitle>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => addCommonHeaders('json')}
                  >
                    + JSON
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => addCommonHeaders('form')}
                  >
                    + Form
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Existing Headers */}
              {Object.entries(request.headers).map(([key, value]) => (
                <div key={key} className="flex gap-2 items-center">
                  <Input value={key} disabled className="flex-1" />
                  <Input
                    value={value}
                    onChange={(e) => handleHeaderValueChange(key, e.target.value)}
                    className="flex-1"
                  />
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleRemoveHeader(key)}
                  >
                    <X className="w-4 h-4" />
                  </Button>
                </div>
              ))}

              {/* Add New Header */}
              <div className="flex gap-2 items-center">
                <Input
                  value={newHeaderKey}
                  onChange={(e) => setNewHeaderKey(e.target.value)}
                  placeholder="Header name"
                  className="flex-1"
                />
                <Input
                  value={newHeaderValue}
                  onChange={(e) => setNewHeaderValue(e.target.value)}
                  placeholder="Header value"
                  className="flex-1"
                />
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleAddHeader}
                  disabled={!newHeaderKey || !newHeaderValue}
                >
                  <Plus className="w-4 h-4" />
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="body">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="text-sm">Request Body</CardTitle>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={formatJSON}
                  >
                    <Code className="w-4 h-4 mr-2" />
                    Format JSON
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <Textarea
                value={request.body || ''}
                onChange={(e) => handleBodyChange(e.target.value)}
                placeholder='{"key": "value"}'
                className="min-h-[200px] font-mono text-sm"
              />
              
              {/* Quick Body Templates */}
              <div className="mt-4 flex gap-2 flex-wrap">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleBodyChange('{\n  "title": "Sample Title",\n  "body": "Sample content",\n  "userId": 1\n}')}
                >
                  JSON Sample
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleBodyChange('name=John&email=<EMAIL>')}
                >
                  Form Data
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="auth">
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">Authentication</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="text-sm text-muted-foreground">
                Add authentication headers manually in the Headers tab, or use these quick options:
              </div>
              
              <div className="flex gap-2 flex-wrap">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => addCommonHeaders('auth')}
                >
                  + Bearer Token
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onUpdateRequest({
                    headers: {
                      ...request.headers,
                      'X-API-Key': 'YOUR_API_KEY_HERE'
                    }
                  })}
                >
                  + API Key
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
