'use client'

import { useState } from 'react'
import {
  DndContext,
  DragEndEvent,
  DragOverlay,
  DragStartEvent,
  closestCenter,
  PointerSensor,
  KeyboardSensor,
  TouchSensor,
  MouseSensor,
  useSensor,
  useSensors
} from '@dnd-kit/core'
import {
  restrictToWindowEdges,
  restrictToParentElement,
  snapCenterToCursor
} from '@dnd-kit/modifiers'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { ComponentPalette } from './component-palette'
import { BuilderCanvas } from './builder-canvas'
import { PropertyPanel } from './property-panel'
import { BuilderHeader } from './builder-header'
import { useUIBuilderStore } from '@/lib/store'
import { UIComponent } from '@/types'
import { generateId } from '@/lib/utils'
import { ToastContainer } from '@/components/ui/toast'

export function UIBuilder() {
  const {
    currentProject,
    selectedComponent,
    draggedComponent,
    isPreviewMode,
    setDraggedComponent,
    addComponent,
    updateComponent,
    addComponentWithHistory,
    updateComponentWithHistory,
  } = useUIBuilderStore()

  const [activeId, setActiveId] = useState<string | null>(null)
  const [snapToGrid, setSnapToGrid] = useState(true)
  const gridSize = 10 // 10px grid

  // Custom snap-to-grid modifier
  const snapToGridModifier = ({ transform }) => {
    if (!snapToGrid) return transform

    return {
      ...transform,
      x: Math.round(transform.x / gridSize) * gridSize,
      y: Math.round(transform.y / gridSize) * gridSize,
    }
  }

  // Configure sensors for better drag and drop experience
  const sensors = useSensors(
    // Mouse sensor for desktop
    useSensor(MouseSensor, {
      activationConstraint: {
        distance: 8, // Require 8px of movement before activating
      },
    }),
    // Touch sensor for mobile devices
    useSensor(TouchSensor, {
      activationConstraint: {
        delay: 200, // 200ms delay before activation
        tolerance: 8, // Allow 8px of movement during delay
      },
    }),
    // Keyboard sensor for accessibility
    useSensor(KeyboardSensor, {
      coordinateGetter: (event, { context: { active, droppableRects, droppableContainers, collisionRect } }) => {
        // Custom keyboard navigation logic could be added here
        return undefined
      },
    })
  )

  const handleDragStart = (event: DragStartEvent) => {
    const { active } = event
    setActiveId(active.id as string)
    
    // If dragging from palette, create a new component
    if (active.data.current?.fromPalette) {
      const componentType = active.data.current.componentType
      const newComponent: UIComponent = {
        id: generateId(),
        project_id: currentProject?.id || '',
        type: componentType,
        props: getDefaultProps(componentType),
        position: { x: 0, y: 0 },
        size: { width: 200, height: 50 },
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      }
      setDraggedComponent(newComponent)
    }
  }

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event
    setActiveId(null)
    setDraggedComponent(null)

    if (!over) return

    // If dropping on canvas from palette
    if (over.id === 'canvas' && draggedComponent && active.id.toString().startsWith('palette-')) {
      // Get the canvas element to calculate relative position
      const canvasElement = document.querySelector('[data-canvas="true"]')
      if (canvasElement) {
        const canvasRect = canvasElement.getBoundingClientRect()

        // Calculate position based on where the drag ended
        let x = event.activatorEvent.clientX - canvasRect.left - (draggedComponent.size.width / 2)
        let y = event.activatorEvent.clientY - canvasRect.top - (draggedComponent.size.height / 2)

        // Apply snap to grid if enabled
        if (snapToGrid) {
          x = Math.round(x / gridSize) * gridSize
          y = Math.round(y / gridSize) * gridSize
        }

        // Ensure component stays within canvas bounds
        x = Math.max(0, Math.min(x, canvasRect.width - draggedComponent.size.width))
        y = Math.max(0, Math.min(y, canvasRect.height - draggedComponent.size.height))

        addComponentWithHistory({
          ...draggedComponent,
          position: { x, y },
        })
      } else {
        // Fallback to simple positioning
        addComponent({
          ...draggedComponent,
          position: { x: 50, y: 50 },
        })
      }
    }

    // If moving an existing component on canvas
    if (over.id === 'canvas' && !active.id.toString().startsWith('palette-') && event.delta) {
      const componentId = active.id.toString()
      const component = currentProject?.components.find(c => c.id === componentId)

      if (component) {
        let newX = component.position.x + event.delta.x
        let newY = component.position.y + event.delta.y

        // Apply snap to grid if enabled
        if (snapToGrid) {
          newX = Math.round(newX / gridSize) * gridSize
          newY = Math.round(newY / gridSize) * gridSize
        }

        // Ensure component stays within canvas bounds
        const canvasElement = document.querySelector('[data-canvas="true"]')
        if (canvasElement) {
          const canvasRect = canvasElement.getBoundingClientRect()
          newX = Math.max(0, Math.min(newX, canvasRect.width - component.size.width))
          newY = Math.max(0, Math.min(newY, canvasRect.height - component.size.height))
        }

        updateComponentWithHistory(componentId, {
          position: { x: newX, y: newY },
          updated_at: new Date().toISOString()
        })
      }
    }
  }

  const getDefaultProps = (componentType: string) => {
    switch (componentType) {
      case 'button':
        return { text: 'Button', variant: 'default', size: 'md' }
      case 'input':
        return { placeholder: 'Enter text...', type: 'text' }
      case 'text':
        return { content: 'Text content', fontSize: 'base' }
      case 'heading':
        return { content: 'Heading', level: 1 }
      default:
        return {}
    }
  }

  const renderPreviewComponent = (component: UIComponent) => {
    switch (component.type) {
      case 'button':
        return (
          <Button
            variant={component.props.variant || 'default'}
            size={component.props.size || 'default'}
            className="w-full"
          >
            {component.props.text || 'Button'}
          </Button>
        )

      case 'input':
        return (
          <input
            placeholder={component.props.placeholder || 'Enter text...'}
            type={component.props.type || 'text'}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        )

      case 'textarea':
        return (
          <textarea
            placeholder={component.props.placeholder || 'Enter text...'}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            rows={component.props.rows || 3}
          />
        )

      case 'text':
        return (
          <p
            className={`text-${component.props.fontSize || 'base'} ${component.props.className || ''}`}
            style={{ color: component.props.color }}
          >
            {component.props.content || 'Text content'}
          </p>
        )

      case 'heading':
        const HeadingTag = `h${component.props.level || 1}` as keyof JSX.IntrinsicElements
        return (
          <HeadingTag
            className={`font-bold ${component.props.className || ''}`}
            style={{ color: component.props.color }}
          >
            {component.props.content || 'Heading'}
          </HeadingTag>
        )

      case 'card':
        return (
          <Card className="w-full">
            <CardHeader>
              <CardTitle>{component.props.title || 'Card Title'}</CardTitle>
            </CardHeader>
            <CardContent>
              <p>{component.props.content || 'Card content goes here...'}</p>
            </CardContent>
          </Card>
        )

      case 'checkbox':
        return (
          <div className="flex items-center space-x-2">
            <input type="checkbox" id={component.id} className="rounded" />
            <label htmlFor={component.id}>
              {component.props.label || 'Checkbox label'}
            </label>
          </div>
        )

      default:
        return (
          <div className="border border-gray-300 rounded p-2 bg-gray-100">
            <Badge variant="secondary">{component.type}</Badge>
          </div>
        )
    }
  }

  if (isPreviewMode) {
    return (
      <div className="h-screen bg-white">
        <BuilderHeader />
        <div className="h-full pt-16 overflow-auto">
          {/* Preview mode content - render actual components */}
          <div className="h-full p-4">
            <div className="max-w-4xl mx-auto bg-white min-h-[600px] relative">
              {currentProject?.components && currentProject.components.length > 0 ? (
                currentProject.components.map((component) => (
                  <div
                    key={component.id}
                    style={{
                      position: 'absolute',
                      left: component.position.x,
                      top: component.position.y,
                      width: component.size.width,
                      minHeight: component.size.height,
                    }}
                  >
                    {renderPreviewComponent(component)}
                  </div>
                ))
              ) : (
                <div className="h-full flex items-center justify-center">
                  <Card className="p-8">
                    <h2 className="text-2xl font-bold mb-4">Preview Mode</h2>
                    <p className="text-muted-foreground">
                      Add components to see them rendered here
                    </p>
                  </Card>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    )
  }

  // Render drag preview based on what's being dragged
  const renderDragOverlay = () => {
    if (!activeId) return null

    // If dragging from palette, show the component type
    if (activeId.startsWith('palette-')) {
      const componentType = activeId.replace('palette-', '')
      return (
        <div className="bg-white dark:bg-gray-800 border-2 border-blue-500 rounded-lg p-3 shadow-lg opacity-90">
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
            <span className="font-medium text-sm capitalize">{componentType}</span>
          </div>
        </div>
      )
    }

    // If dragging an existing component, show its current state
    if (draggedComponent) {
      return (
        <div
          className="bg-white dark:bg-gray-800 border-2 border-blue-500 rounded-lg shadow-lg opacity-90"
          style={{
            width: draggedComponent.size.width,
            minHeight: draggedComponent.size.height,
          }}
        >
          <div className="p-2">
            <Badge variant="secondary" className="text-xs">
              {draggedComponent.type}
            </Badge>
          </div>
        </div>
      )
    }

    return null
  }

  return (
    <DndContext
      sensors={sensors}
      collisionDetection={closestCenter}
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
    >
      <div className="h-screen bg-gray-50 dark:bg-gray-900 flex flex-col">
        <BuilderHeader />
        
        <div className="flex-1 flex overflow-hidden">
          {/* Component Palette */}
          <div className="w-64 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 overflow-y-auto">
            <ComponentPalette />
          </div>

          {/* Main Canvas Area */}
          <div className="flex-1 flex flex-col">
            <div className="flex-1 overflow-auto">
              <BuilderCanvas />
            </div>
          </div>

          {/* Property Panel */}
          <div className="w-80 bg-white dark:bg-gray-800 border-l border-gray-200 dark:border-gray-700 overflow-y-auto">
            <PropertyPanel />
          </div>
        </div>
      </div>

      <DragOverlay
        modifiers={[restrictToWindowEdges, snapToGridModifier]}
        dropAnimation={{
          duration: 300,
          easing: 'cubic-bezier(0.18, 0.67, 0.6, 1.22)',
        }}
      >
        {renderDragOverlay()}
      </DragOverlay>

      <ToastContainer />
    </DndContext>
  )
}
