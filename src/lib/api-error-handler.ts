import { NextResponse } from 'next/server'

export class APIError extends <PERSON><PERSON><PERSON> {
  constructor(
    message: string,
    public statusCode: number = 500,
    public code?: string
  ) {
    super(message)
    this.name = 'APIError'
  }
}

export function handleAPIError(error: unknown) {
  console.error('API Error:', error)

  if (error instanceof APIError) {
    return NextResponse.json(
      { 
        error: error.message,
        code: error.code 
      },
      { status: error.statusCode }
    )
  }

  if (error instanceof Error) {
    // Handle specific database errors
    if (error.message.includes('duplicate key')) {
      return NextResponse.json(
        { error: 'Resource already exists' },
        { status: 409 }
      )
    }

    if (error.message.includes('foreign key')) {
      return NextResponse.json(
        { error: 'Invalid reference' },
        { status: 400 }
      )
    }

    if (error.message.includes('not found')) {
      return NextResponse.json(
        { error: 'Resource not found' },
        { status: 404 }
      )
    }

    return NextResponse.json(
      { error: error.message },
      { status: 500 }
    )
  }

  return NextResponse.json(
    { error: 'Internal server error' },
    { status: 500 }
  )
}

export function validateRequired(data: any, fields: string[]) {
  const missing = fields.filter(field => !data[field])
  if (missing.length > 0) {
    throw new APIError(
      `Missing required fields: ${missing.join(', ')}`,
      400,
      'MISSING_FIELDS'
    )
  }
}

export function validateUUID(id: string, fieldName: string = 'id') {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i
  if (!uuidRegex.test(id)) {
    throw new APIError(
      `Invalid ${fieldName} format`,
      400,
      'INVALID_UUID'
    )
  }
}
