import { create } from 'zustand'
import { devtools, persist } from 'zustand/middleware'
import { createClientComponentClient } from './supabase'

// Command pattern for undo/redo
interface Command {
  execute: () => void
  undo: () => void
  description: string
}

// Command implementations
class AddComponentCommand implements Command {
  constructor(
    private component: UIComponent,
    private store: any
  ) {}

  execute() {
    this.store.getState().addComponent(this.component)
  }

  undo() {
    this.store.getState().removeComponent(this.component.id)
  }

  get description() {
    return `Add ${this.component.type} component`
  }
}

class RemoveComponentCommand implements Command {
  constructor(
    private component: UIComponent,
    private store: any
  ) {}

  execute() {
    this.store.getState().removeComponent(this.component.id)
  }

  undo() {
    this.store.getState().addComponent(this.component)
  }

  get description() {
    return `Remove ${this.component.type} component`
  }
}

class UpdateComponentCommand implements Command {
  constructor(
    private componentId: string,
    private oldProps: Partial<UIComponent>,
    private newProps: Partial<UIComponent>,
    private store: any
  ) {}

  execute() {
    this.store.getState().updateComponent(this.componentId, this.newProps)
  }

  undo() {
    this.store.getState().updateComponent(this.componentId, this.oldProps)
  }

  get description() {
    return `Update component ${this.componentId}`
  }
}

// User and Authentication Store
interface User {
  id: string
  email: string
  subscription_tier: 'free' | 'pro' | 'team' | 'enterprise'
}

interface AuthState {
  user: User | null
  isLoading: boolean
  setUser: (user: User | null) => void
  setLoading: (loading: boolean) => void
  logout: () => void
}

export const useAuthStore = create<AuthState>()(
  devtools(
    (set) => ({
      user: null,
      isLoading: true,
      setUser: (user) => set({ user }),
      setLoading: (isLoading) => set({ isLoading }),
      logout: () => set({ user: null }),
    }),
    { name: 'auth-store' }
  )
)

// UI Builder Store
interface UIComponent {
  id: string
  type: string
  props: Record<string, any>
  position: { x: number; y: number }
  size: { width: number; height: number }
  parent_id?: string
  children?: string[]
}

interface Project {
  id: string
  name: string
  description?: string
  webhook_url?: string
  is_published: boolean
  public_url?: string
  components: UIComponent[]
}

interface UIBuilderState {
  currentProject: Project | null
  selectedComponent: string | null
  draggedComponent: UIComponent | null
  isPreviewMode: boolean
  isSaving: boolean
  isLoading: boolean
  error: string | null
  autoSave: boolean
  lastSaved: string | null
  history: Command[]
  historyIndex: number
  maxHistorySize: number
  setCurrentProject: (project: Project | null) => void
  setSelectedComponent: (componentId: string | null) => void
  setDraggedComponent: (component: UIComponent | null) => void
  togglePreviewMode: () => void
  addComponent: (component: UIComponent) => void
  updateComponent: (componentId: string, updates: Partial<UIComponent>) => void
  removeComponent: (componentId: string) => void
  saveProject: () => Promise<void>
  loadProject: (projectId: string) => Promise<void>
  createProject: (name: string, description?: string) => Promise<void>
  setError: (error: string | null) => void
  setLoading: (loading: boolean) => void
  setSaving: (saving: boolean) => void
  setAutoSave: (autoSave: boolean) => void
  triggerAutoSave: () => void
  executeCommand: (command: Command) => void
  undo: () => void
  redo: () => void
  canUndo: () => boolean
  canRedo: () => boolean
  clearHistory: () => void
}

export const useUIBuilderStore = create<UIBuilderState>()(
  devtools(
    (set, get) => ({
      currentProject: null,
      selectedComponent: null,
      draggedComponent: null,
      isPreviewMode: false,
      isSaving: false,
      isLoading: false,
      error: null,
      autoSave: true,
      lastSaved: null,
      history: [],
      historyIndex: -1,
      maxHistorySize: 50,
      setCurrentProject: (project) => set({ currentProject: project }),
      setSelectedComponent: (componentId) => set({ selectedComponent: componentId }),
      setDraggedComponent: (component) => set({ draggedComponent: component }),
      togglePreviewMode: () => set((state) => ({ isPreviewMode: !state.isPreviewMode })),
      setError: (error) => set({ error }),
      setLoading: (isLoading) => set({ isLoading }),
      setSaving: (isSaving) => set({ isSaving }),
      setAutoSave: (autoSave) => set({ autoSave }),

      triggerAutoSave: () => {
        const state = get()
        if (state.autoSave && state.currentProject && state.currentProject.id !== 'temp-project') {
          // Debounce auto-save to avoid too many requests
          setTimeout(() => {
            const currentState = get()
            if (currentState.currentProject && !currentState.isSaving) {
              currentState.saveProject()
            }
          }, 1000)
        }
      },

      // Command pattern implementation
      executeCommand: (command: Command) => {
        const state = get()

        // Execute the command
        command.execute()

        // Add to history
        const newHistory = state.history.slice(0, state.historyIndex + 1)
        newHistory.push(command)

        // Limit history size
        if (newHistory.length > state.maxHistorySize) {
          newHistory.shift()
        }

        set({
          history: newHistory,
          historyIndex: newHistory.length - 1
        })
      },

      undo: () => {
        const state = get()
        if (state.historyIndex >= 0) {
          const command = state.history[state.historyIndex]
          command.undo()
          set({ historyIndex: state.historyIndex - 1 })
        }
      },

      redo: () => {
        const state = get()
        if (state.historyIndex < state.history.length - 1) {
          const command = state.history[state.historyIndex + 1]
          command.execute()
          set({ historyIndex: state.historyIndex + 1 })
        }
      },

      canUndo: () => {
        const state = get()
        return state.historyIndex >= 0
      },

      canRedo: () => {
        const state = get()
        return state.historyIndex < state.history.length - 1
      },

      clearHistory: () => {
        set({ history: [], historyIndex: -1 })
      },
      addComponent: (component) => {
        set((state) => {
          if (!state.currentProject) return state
          return {
            currentProject: {
              ...state.currentProject,
              components: [...state.currentProject.components, component],
            },
          }
        })
        // Trigger auto-save after adding component
        get().triggerAutoSave()
      },

      // Command-based operations for undo/redo
      addComponentWithHistory: (component) => {
        const command = new AddComponentCommand(component, { getState: get })
        get().executeCommand(command)
        get().triggerAutoSave()
      },

      removeComponentWithHistory: (componentId) => {
        const state = get()
        const component = state.currentProject?.components.find(c => c.id === componentId)
        if (component) {
          const command = new RemoveComponentCommand(component, { getState: get })
          get().executeCommand(command)
          get().triggerAutoSave()
        }
      },

      updateComponentWithHistory: (componentId, updates) => {
        const state = get()
        const component = state.currentProject?.components.find(c => c.id === componentId)
        if (component) {
          const oldProps = { ...component }
          const newProps = { ...component, ...updates }
          const command = new UpdateComponentCommand(componentId, oldProps, newProps, { getState: get })
          get().executeCommand(command)
          get().triggerAutoSave()
        }
      },
      updateComponent: (componentId, updates) => {
        set((state) => {
          if (!state.currentProject) return state
          return {
            currentProject: {
              ...state.currentProject,
              components: state.currentProject.components.map((comp) =>
                comp.id === componentId ? { ...comp, ...updates } : comp
              ),
            },
          }
        })
        // Trigger auto-save after updating component
        get().triggerAutoSave()
      },
      removeComponent: (componentId) => {
        set((state) => {
          if (!state.currentProject) return state
          return {
            currentProject: {
              ...state.currentProject,
              components: state.currentProject.components.filter((comp) => comp.id !== componentId),
            },
          }
        })
        // Trigger auto-save after removing component
        get().triggerAutoSave()
      },

      // Project management functions
      saveProject: async () => {
        const state = get()
        if (!state.currentProject) return

        set({ isSaving: true, error: null })

        try {
          const response = await fetch(`/api/projects/${state.currentProject.id}`, {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              name: state.currentProject.name,
              description: state.currentProject.description,
              webhook_url: state.currentProject.webhook_url,
              components: state.currentProject.components,
            }),
          })

          if (!response.ok) {
            const errorData = await response.json()
            throw new Error(errorData.error || 'Failed to save project')
          }

          const { project } = await response.json()
          set({
            currentProject: project,
            isSaving: false,
            lastSaved: new Date().toISOString()
          })

          // Show success toast
          if (typeof window !== 'undefined' && (window as any).addToast) {
            ;(window as any).addToast({
              type: 'success',
              title: 'Project saved',
              description: 'Your changes have been saved successfully'
            })
          }
        } catch (error) {
          console.error('Error saving project:', error)
          const errorMessage = error instanceof Error ? error.message : 'Failed to save project'
          set({
            error: errorMessage,
            isSaving: false
          })

          // Show error toast
          if (typeof window !== 'undefined' && (window as any).addToast) {
            ;(window as any).addToast({
              type: 'error',
              title: 'Save failed',
              description: errorMessage
            })
          }
        }
      },

      loadProject: async (projectId: string) => {
        set({ isLoading: true, error: null })

        try {
          const response = await fetch(`/api/projects/${projectId}`)

          if (!response.ok) {
            const errorData = await response.json()
            throw new Error(errorData.error || 'Failed to load project')
          }

          const { project } = await response.json()
          set({ currentProject: project, isLoading: false })
        } catch (error) {
          console.error('Error loading project:', error)
          set({
            error: error instanceof Error ? error.message : 'Failed to load project',
            isLoading: false
          })
        }
      },

      createProject: async (name: string, description?: string) => {
        set({ isSaving: true, error: null })

        try {
          const response = await fetch('/api/projects', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              name,
              description,
              components: [],
            }),
          })

          if (!response.ok) {
            const errorData = await response.json()
            throw new Error(errorData.error || 'Failed to create project')
          }

          const { project } = await response.json()
          set({ currentProject: project, isSaving: false })
        } catch (error) {
          console.error('Error creating project:', error)
          set({
            error: error instanceof Error ? error.message : 'Failed to create project',
            isSaving: false
          })
        }
      },
    }),
    { name: 'ui-builder-store' }
  )
)

// API Testing Store
interface APIRequest {
  id: string
  name: string
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'
  url: string
  headers: Record<string, string>
  body?: string
}

interface APITestingState {
  requests: APIRequest[]
  currentRequest: APIRequest | null
  isLoading: boolean
  lastResponse: any
  addRequest: (request: APIRequest) => void
  updateRequest: (requestId: string, updates: Partial<APIRequest>) => void
  removeRequest: (requestId: string) => void
  setCurrentRequest: (request: APIRequest | null) => void
  setLoading: (loading: boolean) => void
  setLastResponse: (response: any) => void
}

export const useAPITestingStore = create<APITestingState>()(
  devtools(
    persist(
      (set) => ({
        requests: [],
        currentRequest: null,
        isLoading: false,
        lastResponse: null,
        addRequest: (request) =>
          set((state) => ({ requests: [...state.requests, request] })),
        updateRequest: (requestId, updates) =>
          set((state) => ({
            requests: state.requests.map((req) =>
              req.id === requestId ? { ...req, ...updates } : req
            ),
          })),
        removeRequest: (requestId) =>
          set((state) => ({
            requests: state.requests.filter((req) => req.id !== requestId),
          })),
        setCurrentRequest: (request) => set({ currentRequest: request }),
        setLoading: (isLoading) => set({ isLoading }),
        setLastResponse: (lastResponse) => set({ lastResponse }),
      }),
      {
        name: 'api-testing-storage',
        partialize: (state) => ({ requests: state.requests }),
      }
    ),
    { name: 'api-testing-store' }
  )
)
