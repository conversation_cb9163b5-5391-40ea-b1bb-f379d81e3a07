import { NextRequest, NextResponse } from 'next/server'
import { createRouteHandlerClient } from '@/lib/supabase-server'

export async function GET(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient(request)
    
    // Get the current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError) {
      return NextResponse.json({ 
        error: 'Authentication error',
        details: authError 
      }, { status: 401 })
    }

    if (!user) {
      return NextResponse.json({ 
        error: 'No user found' 
      }, { status: 401 })
    }

    // Get user's organizations
    const { data: orgMembers, error: orgError } = await supabase
      .from('organization_members')
      .select(`
        org_id,
        role,
        organizations (
          id,
          name,
          owner_id
        )
      `)
      .eq('user_id', user.id)

    // Get user record from our users table
    const { data: userRecord, error: userError } = await supabase
      .from('users')
      .select('*')
      .eq('id', user.id)
      .single()

    return NextResponse.json({
      auth_user: {
        id: user.id,
        email: user.email,
        created_at: user.created_at
      },
      user_record: userRecord,
      user_record_error: userError,
      organizations: orgMembers,
      org_error: orgError,
      debug_info: {
        has_organizations: orgMembers && orgMembers.length > 0,
        org_count: orgMembers ? orgMembers.length : 0
      }
    })
  } catch (error) {
    console.error('Debug user error:', error)
    return NextResponse.json({ 
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
