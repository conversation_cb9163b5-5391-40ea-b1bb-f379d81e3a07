import { NextRequest, NextResponse } from 'next/server'
import { createRouteHandlerClient } from '@/lib/supabase-server'

export async function POST(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient(request)
    
    // Get the current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user exists in our users table
    const { data: existingUser, error: userError } = await supabase
      .from('users')
      .select('id')
      .eq('id', user.id)
      .single()

    if (userError && userError.code !== 'PGRST116') {
      console.error('Error checking user:', userError)
      return NextResponse.json({ error: 'Database error' }, { status: 500 })
    }

    if (!existingUser) {
      console.log('User not found in users table, creating...')
      
      // Create user in our users table
      const { data: newUser, error: createUserError } = await supabase
        .from('users')
        .insert({
          id: user.id,
          email: user.email || '',
          subscription_tier: 'free'
        })
        .select()
        .single()

      if (createUserError) {
        console.error('Error creating user:', createUserError)
        return NextResponse.json({ error: 'Failed to create user' }, { status: 500 })
      }

      console.log('User created successfully:', newUser.id)
    }

    // Check if user has an organization
    const { data: orgMembers, error: orgError } = await supabase
      .from('organization_members')
      .select('org_id')
      .eq('user_id', user.id)
      .limit(1)

    if (orgError) {
      console.error('Error checking organizations:', orgError)
      return NextResponse.json({ error: 'Failed to check organizations' }, { status: 500 })
    }

    if (!orgMembers || orgMembers.length === 0) {
      console.log('No organization found, creating default organization...')
      
      // Create default organization
      const { data: newOrg, error: createOrgError } = await supabase
        .from('organizations')
        .insert({
          name: `${user.email}'s Organization`,
          owner_id: user.id
        })
        .select()
        .single()

      if (createOrgError) {
        console.error('Error creating organization:', createOrgError)
        return NextResponse.json({ error: 'Failed to create organization' }, { status: 500 })
      }

      // Add user as owner of the organization
      const { error: memberError } = await supabase
        .from('organization_members')
        .insert({
          org_id: newOrg.id,
          user_id: user.id,
          role: 'owner'
        })

      if (memberError) {
        console.error('Error creating organization membership:', memberError)
        return NextResponse.json({ error: 'Failed to create organization membership' }, { status: 500 })
      }

      console.log('Organization created successfully:', newOrg.id)
    }

    return NextResponse.json({ 
      success: true,
      message: 'User and organization ensured'
    })
  } catch (error) {
    console.error('Error ensuring user:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
