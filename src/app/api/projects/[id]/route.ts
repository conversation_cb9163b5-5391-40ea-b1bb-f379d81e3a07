import { NextRequest, NextResponse } from 'next/server'
import { createRouteHandlerClient } from '@/lib/supabase-server'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = createRouteHandlerClient(request)
    
    // Get the current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const projectId = params.id

    // Get the project with components
    const { data: project, error: projectError } = await supabase
      .from('projects')
      .select(`
        id,
        name,
        description,
        webhook_url,
        config,
        is_published,
        public_url,
        created_at,
        updated_at,
        org_id,
        ui_components (
          id,
          type,
          props,
          position,
          size,
          parent_id,
          created_at,
          updated_at
        )
      `)
      .eq('id', projectId)
      .single()

    if (projectError) {
      if (projectError.code === 'PGRST116') {
        return NextResponse.json({ error: 'Project not found' }, { status: 404 })
      }
      return NextResponse.json({ error: 'Failed to fetch project' }, { status: 500 })
    }

    // Check if user has access to this project
    const { data: orgMember, error: orgError } = await supabase
      .from('organization_members')
      .select('org_id')
      .eq('user_id', user.id)
      .eq('org_id', project.org_id)
      .single()

    if (orgError || !orgMember) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 })
    }

    // Transform the data
    const result = {
      id: project.id,
      name: project.name,
      description: project.description,
      webhook_url: project.webhook_url,
      is_published: project.is_published,
      public_url: project.public_url,
      created_at: project.created_at,
      updated_at: project.updated_at,
      components: project.ui_components.map(component => ({
        id: component.id,
        project_id: project.id,
        type: component.type,
        props: component.props,
        position: component.position,
        size: component.size,
        parent_id: component.parent_id,
        created_at: component.created_at,
        updated_at: component.updated_at,
      }))
    }

    return NextResponse.json({ project: result })
  } catch (error) {
    console.error('Error fetching project:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = createRouteHandlerClient(request)
    
    // Get the current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const projectId = params.id
    const body = await request.json()
    const { name, description, webhook_url, components = [] } = body

    // First, verify the project exists and user has access
    const { data: project, error: projectError } = await supabase
      .from('projects')
      .select('id, org_id')
      .eq('id', projectId)
      .single()

    if (projectError) {
      if (projectError.code === 'PGRST116') {
        return NextResponse.json({ error: 'Project not found' }, { status: 404 })
      }
      return NextResponse.json({ error: 'Failed to fetch project' }, { status: 500 })
    }

    // Check if user has access to this project
    const { data: orgMember, error: orgError } = await supabase
      .from('organization_members')
      .select('org_id')
      .eq('user_id', user.id)
      .eq('org_id', project.org_id)
      .single()

    if (orgError || !orgMember) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 })
    }

    // Update the project
    const updateData: any = { updated_at: new Date().toISOString() }
    if (name !== undefined) updateData.name = name
    if (description !== undefined) updateData.description = description
    if (webhook_url !== undefined) updateData.webhook_url = webhook_url

    const { data: updatedProject, error: updateError } = await supabase
      .from('projects')
      .update(updateData)
      .eq('id', projectId)
      .select()
      .single()

    if (updateError) {
      console.error('Error updating project:', updateError)
      return NextResponse.json({ error: 'Failed to update project' }, { status: 500 })
    }

    // Handle components update
    let updatedComponents = []
    if (components.length >= 0) {
      // Delete existing components
      await supabase
        .from('ui_components')
        .delete()
        .eq('project_id', projectId)

      // Insert new components
      if (components.length > 0) {
        const componentsToInsert = components.map(component => ({
          project_id: projectId,
          type: component.type,
          props: component.props,
          position: component.position,
          size: component.size,
          parent_id: component.parent_id || null
        }))

        const { data: insertedComponents, error: componentsError } = await supabase
          .from('ui_components')
          .insert(componentsToInsert)
          .select()

        if (componentsError) {
          console.error('Error updating components:', componentsError)
        } else {
          updatedComponents = insertedComponents
        }
      }
    }

    const result = {
      id: updatedProject.id,
      name: updatedProject.name,
      description: updatedProject.description,
      webhook_url: updatedProject.webhook_url,
      is_published: updatedProject.is_published,
      public_url: updatedProject.public_url,
      created_at: updatedProject.created_at,
      updated_at: updatedProject.updated_at,
      components: updatedComponents.map(component => ({
        id: component.id,
        project_id: component.project_id,
        type: component.type,
        props: component.props,
        position: component.position,
        size: component.size,
        parent_id: component.parent_id,
        created_at: component.created_at,
        updated_at: component.updated_at,
      }))
    }

    return NextResponse.json({ project: result })
  } catch (error) {
    console.error('Error updating project:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = createRouteHandlerClient(request)
    
    // Get the current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const projectId = params.id

    // First, verify the project exists and user has access
    const { data: project, error: projectError } = await supabase
      .from('projects')
      .select('id, org_id')
      .eq('id', projectId)
      .single()

    if (projectError) {
      if (projectError.code === 'PGRST116') {
        return NextResponse.json({ error: 'Project not found' }, { status: 404 })
      }
      return NextResponse.json({ error: 'Failed to fetch project' }, { status: 500 })
    }

    // Check if user has access to this project
    const { data: orgMember, error: orgError } = await supabase
      .from('organization_members')
      .select('org_id, role')
      .eq('user_id', user.id)
      .eq('org_id', project.org_id)
      .single()

    if (orgError || !orgMember || !['owner', 'admin'].includes(orgMember.role)) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 })
    }

    // Delete the project (components will be deleted via CASCADE)
    const { error: deleteError } = await supabase
      .from('projects')
      .delete()
      .eq('id', projectId)

    if (deleteError) {
      console.error('Error deleting project:', deleteError)
      return NextResponse.json({ error: 'Failed to delete project' }, { status: 500 })
    }

    return NextResponse.json({ message: 'Project deleted successfully' })
  } catch (error) {
    console.error('Error deleting project:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
