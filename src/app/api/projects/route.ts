import { NextRequest, NextResponse } from 'next/server'
import { createRouteHandlerClient } from '@/lib/supabase-server'

export async function GET(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient(request)
    
    // Get the current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get user's organizations
    const { data: orgMembers, error: orgError } = await supabase
      .from('organization_members')
      .select('org_id')
      .eq('user_id', user.id)

    if (orgError) {
      return NextResponse.json({ error: 'Failed to fetch organizations' }, { status: 500 })
    }

    const orgIds = orgMembers.map(member => member.org_id)

    // Get projects from user's organizations
    const { data: projects, error: projectsError } = await supabase
      .from('projects')
      .select(`
        id,
        name,
        description,
        webhook_url,
        config,
        is_published,
        public_url,
        created_at,
        updated_at,
        ui_components (
          id,
          type,
          props,
          position,
          size,
          parent_id,
          created_at,
          updated_at
        )
      `)
      .in('org_id', orgIds)
      .order('updated_at', { ascending: false })

    if (projectsError) {
      return NextResponse.json({ error: 'Failed to fetch projects' }, { status: 500 })
    }

    // Transform the data to match our frontend interface
    const transformedProjects = projects.map(project => ({
      id: project.id,
      name: project.name,
      description: project.description,
      webhook_url: project.webhook_url,
      is_published: project.is_published,
      public_url: project.public_url,
      created_at: project.created_at,
      updated_at: project.updated_at,
      components: project.ui_components.map(component => ({
        id: component.id,
        project_id: project.id,
        type: component.type,
        props: component.props,
        position: component.position,
        size: component.size,
        parent_id: component.parent_id,
        created_at: component.created_at,
        updated_at: component.updated_at,
      }))
    }))

    return NextResponse.json({ projects: transformedProjects })
  } catch (error) {
    console.error('Error fetching projects:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient(request)
    
    // Get the current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { name, description, webhook_url, components = [] } = body

    if (!name) {
      return NextResponse.json({ error: 'Project name is required' }, { status: 400 })
    }

    // Get user's default organization (first one they're a member of)
    const { data: orgMembers, error: orgError } = await supabase
      .from('organization_members')
      .select('org_id')
      .eq('user_id', user.id)
      .limit(1)

    if (orgError || !orgMembers.length) {
      return NextResponse.json({ error: 'No organization found' }, { status: 400 })
    }

    const orgId = orgMembers[0].org_id

    // Create the project
    const { data: project, error: projectError } = await supabase
      .from('projects')
      .insert({
        org_id: orgId,
        name,
        description,
        webhook_url,
        config: {}
      })
      .select()
      .single()

    if (projectError) {
      console.error('Error creating project:', projectError)
      return NextResponse.json({ error: 'Failed to create project' }, { status: 500 })
    }

    // Create components if provided
    let createdComponents = []
    if (components.length > 0) {
      const componentsToInsert = components.map(component => ({
        project_id: project.id,
        type: component.type,
        props: component.props,
        position: component.position,
        size: component.size,
        parent_id: component.parent_id || null
      }))

      const { data: insertedComponents, error: componentsError } = await supabase
        .from('ui_components')
        .insert(componentsToInsert)
        .select()

      if (componentsError) {
        console.error('Error creating components:', componentsError)
        // Don't fail the entire request, just log the error
      } else {
        createdComponents = insertedComponents
      }
    }

    const result = {
      id: project.id,
      name: project.name,
      description: project.description,
      webhook_url: project.webhook_url,
      is_published: project.is_published,
      public_url: project.public_url,
      created_at: project.created_at,
      updated_at: project.updated_at,
      components: createdComponents.map(component => ({
        id: component.id,
        project_id: component.project_id,
        type: component.type,
        props: component.props,
        position: component.position,
        size: component.size,
        parent_id: component.parent_id,
        created_at: component.created_at,
        updated_at: component.updated_at,
      }))
    }

    return NextResponse.json({ project: result }, { status: 201 })
  } catch (error) {
    console.error('Error creating project:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
