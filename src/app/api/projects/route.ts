import { NextRequest, NextResponse } from 'next/server'
import { createRouteHandlerClient } from '@/lib/supabase-server'
import { createClient } from '@supabase/supabase-js'

export async function GET(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient(request)
    
    // Get the current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get user's organizations
    const { data: orgMembers, error: orgError } = await supabase
      .from('organization_members')
      .select('org_id')
      .eq('user_id', user.id)

    if (orgError) {
      console.error('Error fetching organizations:', orgError)
      return NextResponse.json({ error: 'Failed to fetch organizations' }, { status: 500 })
    }

    if (!orgMembers || orgMembers.length === 0) {
      console.log('No organizations found for user, returning empty projects list')
      return NextResponse.json({ projects: [] })
    }

    const orgIds = orgMembers.map(member => member.org_id)

    // Get projects from user's organizations
    const { data: projects, error: projectsError } = await supabase
      .from('projects')
      .select(`
        id,
        name,
        description,
        webhook_url,
        config,
        is_published,
        public_url,
        created_at,
        updated_at,
        ui_components (
          id,
          type,
          props,
          position,
          size,
          parent_id,
          created_at,
          updated_at
        )
      `)
      .in('org_id', orgIds)
      .order('updated_at', { ascending: false })

    if (projectsError) {
      return NextResponse.json({ error: 'Failed to fetch projects' }, { status: 500 })
    }

    // Transform the data to match our frontend interface
    const transformedProjects = projects.map(project => ({
      id: project.id,
      name: project.name,
      description: project.description,
      webhook_url: project.webhook_url,
      is_published: project.is_published,
      public_url: project.public_url,
      created_at: project.created_at,
      updated_at: project.updated_at,
      components: project.ui_components.map(component => ({
        id: component.id,
        project_id: project.id,
        type: component.type,
        props: component.props,
        position: component.position,
        size: component.size,
        parent_id: component.parent_id,
        created_at: component.created_at,
        updated_at: component.updated_at,
      }))
    }))

    return NextResponse.json({ projects: transformedProjects })
  } catch (error) {
    console.error('Error fetching projects:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient(request)
    
    // Get the current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { name, description, webhook_url, components = [] } = body

    if (!name) {
      return NextResponse.json({ error: 'Project name is required' }, { status: 400 })
    }

    // Get user's default organization (first one they're a member of)
    let { data: orgMembers, error: orgError } = await supabase
      .from('organization_members')
      .select('org_id')
      .eq('user_id', user.id)
      .limit(1)

    let orgId: string

    if (orgError || !orgMembers.length) {
      // Create a default organization for the user if none exists
      console.log('No organization found for user, creating default organization')

      // Use admin client for organization creation
      const adminClient = createClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.SUPABASE_SERVICE_ROLE_KEY!,
        {
          auth: {
            autoRefreshToken: false,
            persistSession: false
          }
        }
      )

      // First ensure user exists in users table
      const { data: existingUser, error: userCheckError } = await adminClient
        .from('users')
        .select('id')
        .eq('id', user.id)
        .single()

      if (userCheckError && userCheckError.code === 'PGRST116') {
        // User doesn't exist, create them
        const { error: createUserError } = await adminClient
          .from('users')
          .insert({
            id: user.id,
            email: user.email || '',
            subscription_tier: 'free'
          })

        if (createUserError) {
          console.error('Error creating user:', createUserError)
          return NextResponse.json({
            error: 'Failed to create user account',
            details: createUserError.message
          }, { status: 500 })
        }
      }

      const { data: newOrg, error: createOrgError } = await adminClient
        .from('organizations')
        .insert({
          name: `${user.email}'s Organization`,
          owner_id: user.id
        })
        .select()
        .single()

      if (createOrgError) {
        console.error('Error creating organization:', createOrgError)
        return NextResponse.json({
          error: 'Failed to create organization',
          details: createOrgError.message
        }, { status: 500 })
      }

      // Add user as owner of the organization
      const { error: memberError } = await adminClient
        .from('organization_members')
        .insert({
          org_id: newOrg.id,
          user_id: user.id,
          role: 'owner'
        })

      if (memberError) {
        console.error('Error creating organization membership:', memberError)
        return NextResponse.json({
          error: 'Failed to create organization membership',
          details: memberError.message
        }, { status: 500 })
      }

      orgId = newOrg.id
    } else {
      orgId = orgMembers[0].org_id
    }

    // Create the project
    const { data: project, error: projectError } = await supabase
      .from('projects')
      .insert({
        org_id: orgId,
        name,
        description,
        webhook_url,
        config: {}
      })
      .select()
      .single()

    if (projectError) {
      console.error('Error creating project:', projectError)
      return NextResponse.json({ error: 'Failed to create project' }, { status: 500 })
    }

    // Create components if provided
    let createdComponents = []
    if (components.length > 0) {
      const componentsToInsert = components.map(component => ({
        project_id: project.id,
        type: component.type,
        props: component.props,
        position: component.position,
        size: component.size,
        parent_id: component.parent_id || null
      }))

      const { data: insertedComponents, error: componentsError } = await supabase
        .from('ui_components')
        .insert(componentsToInsert)
        .select()

      if (componentsError) {
        console.error('Error creating components:', componentsError)
        // Don't fail the entire request, just log the error
      } else {
        createdComponents = insertedComponents
      }
    }

    const result = {
      id: project.id,
      name: project.name,
      description: project.description,
      webhook_url: project.webhook_url,
      is_published: project.is_published,
      public_url: project.public_url,
      created_at: project.created_at,
      updated_at: project.updated_at,
      components: createdComponents.map(component => ({
        id: component.id,
        project_id: component.project_id,
        type: component.type,
        props: component.props,
        position: component.position,
        size: component.size,
        parent_id: component.parent_id,
        created_at: component.created_at,
        updated_at: component.updated_at,
      }))
    }

    return NextResponse.json({ project: result }, { status: 201 })
  } catch (error) {
    console.error('Error creating project:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
