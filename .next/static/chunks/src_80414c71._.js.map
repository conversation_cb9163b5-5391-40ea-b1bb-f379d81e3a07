{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/FlowUi/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\nimport { createBrowserClient, createServerClient } from '@supabase/ssr'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n\n// Client-side Supabase client\nexport const createClientComponentClient = () => {\n  return createBrowserClient(supabaseUrl, supabaseAnonKey)\n}\n\n// Legacy client for backward compatibility (client-side only)\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey)\n\n// Database types (will be generated later)\nexport type Database = {\n  public: {\n    Tables: {\n      users: {\n        Row: {\n          id: string\n          email: string\n          created_at: string\n          subscription_tier: string\n        }\n        Insert: {\n          id?: string\n          email: string\n          created_at?: string\n          subscription_tier?: string\n        }\n        Update: {\n          id?: string\n          email?: string\n          created_at?: string\n          subscription_tier?: string\n        }\n      }\n      // More tables will be added as we implement them\n    }\n  }\n}\n"], "names": [], "mappings": ";;;;AAGoB;AAHpB;AACA;AAAA;;;AAEA,MAAM;AACN,MAAM;AAGC,MAAM,8BAA8B;IACzC,OAAO,CAAA,GAAA,6KAAA,CAAA,sBAAmB,AAAD,EAAE,aAAa;AAC1C;AAGO,MAAM,WAAW,CAAA,GAAA,0LAAA,CAAA,eAAY,AAAD,EAAE,aAAa", "debugId": null}}, {"offset": {"line": 32, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/FlowUi/src/lib/store.ts"], "sourcesContent": ["import { create } from 'zustand'\nimport { devtools, persist } from 'zustand/middleware'\nimport { createClientComponentClient } from './supabase'\n\n// Command pattern for undo/redo\ninterface Command {\n  execute: () => void\n  undo: () => void\n  description: string\n}\n\n// Command implementations\nclass AddComponentCommand implements Command {\n  constructor(\n    private component: UIComponent,\n    private store: any\n  ) {}\n\n  execute() {\n    this.store.getState().addComponent(this.component)\n  }\n\n  undo() {\n    this.store.getState().removeComponent(this.component.id)\n  }\n\n  get description() {\n    return `Add ${this.component.type} component`\n  }\n}\n\nclass RemoveComponentCommand implements Command {\n  constructor(\n    private component: UIComponent,\n    private store: any\n  ) {}\n\n  execute() {\n    this.store.getState().removeComponent(this.component.id)\n  }\n\n  undo() {\n    this.store.getState().addComponent(this.component)\n  }\n\n  get description() {\n    return `Remove ${this.component.type} component`\n  }\n}\n\nclass UpdateComponentCommand implements Command {\n  constructor(\n    private componentId: string,\n    private oldProps: Partial<UIComponent>,\n    private newProps: Partial<UIComponent>,\n    private store: any\n  ) {}\n\n  execute() {\n    this.store.getState().updateComponent(this.componentId, this.newProps)\n  }\n\n  undo() {\n    this.store.getState().updateComponent(this.componentId, this.oldProps)\n  }\n\n  get description() {\n    return `Update component ${this.componentId}`\n  }\n}\n\n// User and Authentication Store\ninterface User {\n  id: string\n  email: string\n  subscription_tier: 'free' | 'pro' | 'team' | 'enterprise'\n}\n\ninterface AuthState {\n  user: User | null\n  isLoading: boolean\n  setUser: (user: User | null) => void\n  setLoading: (loading: boolean) => void\n  logout: () => void\n}\n\nexport const useAuthStore = create<AuthState>()(\n  devtools(\n    (set) => ({\n      user: null,\n      isLoading: true,\n      setUser: (user) => set({ user }),\n      setLoading: (isLoading) => set({ isLoading }),\n      logout: () => set({ user: null }),\n    }),\n    { name: 'auth-store' }\n  )\n)\n\n// UI Builder Store\ninterface UIComponent {\n  id: string\n  type: string\n  props: Record<string, any>\n  position: { x: number; y: number }\n  size: { width: number; height: number }\n  parent_id?: string\n  children?: string[]\n}\n\ninterface Project {\n  id: string\n  name: string\n  description?: string\n  webhook_url?: string\n  is_published: boolean\n  public_url?: string\n  components: UIComponent[]\n}\n\ninterface UIBuilderState {\n  currentProject: Project | null\n  selectedComponent: string | null\n  draggedComponent: UIComponent | null\n  isPreviewMode: boolean\n  isSaving: boolean\n  isLoading: boolean\n  error: string | null\n  autoSave: boolean\n  lastSaved: string | null\n  history: Command[]\n  historyIndex: number\n  maxHistorySize: number\n  setCurrentProject: (project: Project | null) => void\n  setSelectedComponent: (componentId: string | null) => void\n  setDraggedComponent: (component: UIComponent | null) => void\n  togglePreviewMode: () => void\n  addComponent: (component: UIComponent) => void\n  updateComponent: (componentId: string, updates: Partial<UIComponent>) => void\n  removeComponent: (componentId: string) => void\n  saveProject: () => Promise<void>\n  loadProject: (projectId: string) => Promise<void>\n  createProject: (name: string, description?: string) => Promise<void>\n  setError: (error: string | null) => void\n  setLoading: (loading: boolean) => void\n  setSaving: (saving: boolean) => void\n  setAutoSave: (autoSave: boolean) => void\n  triggerAutoSave: () => void\n  executeCommand: (command: Command) => void\n  undo: () => void\n  redo: () => void\n  canUndo: () => boolean\n  canRedo: () => boolean\n  clearHistory: () => void\n}\n\nexport const useUIBuilderStore = create<UIBuilderState>()(\n  devtools(\n    (set, get) => ({\n      currentProject: null,\n      selectedComponent: null,\n      draggedComponent: null,\n      isPreviewMode: false,\n      isSaving: false,\n      isLoading: false,\n      error: null,\n      autoSave: true,\n      lastSaved: null,\n      history: [],\n      historyIndex: -1,\n      maxHistorySize: 50,\n      setCurrentProject: (project) => set({ currentProject: project }),\n      setSelectedComponent: (componentId) => set({ selectedComponent: componentId }),\n      setDraggedComponent: (component) => set({ draggedComponent: component }),\n      togglePreviewMode: () => set((state) => ({ isPreviewMode: !state.isPreviewMode })),\n      setError: (error) => set({ error }),\n      setLoading: (isLoading) => set({ isLoading }),\n      setSaving: (isSaving) => set({ isSaving }),\n      setAutoSave: (autoSave) => set({ autoSave }),\n\n      triggerAutoSave: () => {\n        const state = get()\n        if (state.autoSave && state.currentProject && state.currentProject.id !== 'temp-project') {\n          // Debounce auto-save to avoid too many requests\n          setTimeout(() => {\n            const currentState = get()\n            if (currentState.currentProject && !currentState.isSaving) {\n              currentState.saveProject()\n            }\n          }, 1000)\n        }\n      },\n\n      // Command pattern implementation\n      executeCommand: (command: Command) => {\n        const state = get()\n\n        // Execute the command\n        command.execute()\n\n        // Add to history\n        const newHistory = state.history.slice(0, state.historyIndex + 1)\n        newHistory.push(command)\n\n        // Limit history size\n        if (newHistory.length > state.maxHistorySize) {\n          newHistory.shift()\n        }\n\n        set({\n          history: newHistory,\n          historyIndex: newHistory.length - 1\n        })\n      },\n\n      undo: () => {\n        const state = get()\n        if (state.historyIndex >= 0) {\n          const command = state.history[state.historyIndex]\n          command.undo()\n          set({ historyIndex: state.historyIndex - 1 })\n        }\n      },\n\n      redo: () => {\n        const state = get()\n        if (state.historyIndex < state.history.length - 1) {\n          const command = state.history[state.historyIndex + 1]\n          command.execute()\n          set({ historyIndex: state.historyIndex + 1 })\n        }\n      },\n\n      canUndo: () => {\n        const state = get()\n        return state.historyIndex >= 0\n      },\n\n      canRedo: () => {\n        const state = get()\n        return state.historyIndex < state.history.length - 1\n      },\n\n      clearHistory: () => {\n        set({ history: [], historyIndex: -1 })\n      },\n      addComponent: (component) => {\n        set((state) => {\n          if (!state.currentProject) return state\n          return {\n            currentProject: {\n              ...state.currentProject,\n              components: [...state.currentProject.components, component],\n            },\n          }\n        })\n        // Trigger auto-save after adding component\n        get().triggerAutoSave()\n      },\n\n      // Command-based operations for undo/redo\n      addComponentWithHistory: (component) => {\n        const command = new AddComponentCommand(component, { getState: get })\n        get().executeCommand(command)\n        get().triggerAutoSave()\n      },\n\n      removeComponentWithHistory: (componentId) => {\n        const state = get()\n        const component = state.currentProject?.components.find(c => c.id === componentId)\n        if (component) {\n          const command = new RemoveComponentCommand(component, { getState: get })\n          get().executeCommand(command)\n          get().triggerAutoSave()\n        }\n      },\n\n      updateComponentWithHistory: (componentId, updates) => {\n        const state = get()\n        const component = state.currentProject?.components.find(c => c.id === componentId)\n        if (component) {\n          const oldProps = { ...component }\n          const newProps = { ...component, ...updates }\n          const command = new UpdateComponentCommand(componentId, oldProps, newProps, { getState: get })\n          get().executeCommand(command)\n          get().triggerAutoSave()\n        }\n      },\n      updateComponent: (componentId, updates) => {\n        set((state) => {\n          if (!state.currentProject) return state\n          return {\n            currentProject: {\n              ...state.currentProject,\n              components: state.currentProject.components.map((comp) =>\n                comp.id === componentId ? { ...comp, ...updates } : comp\n              ),\n            },\n          }\n        })\n        // Trigger auto-save after updating component\n        get().triggerAutoSave()\n      },\n      removeComponent: (componentId) => {\n        set((state) => {\n          if (!state.currentProject) return state\n          return {\n            currentProject: {\n              ...state.currentProject,\n              components: state.currentProject.components.filter((comp) => comp.id !== componentId),\n            },\n          }\n        })\n        // Trigger auto-save after removing component\n        get().triggerAutoSave()\n      },\n\n      // Project management functions\n      saveProject: async () => {\n        const state = get()\n        if (!state.currentProject) {\n          console.log('No current project to save')\n          return\n        }\n\n        console.log('Saving project:', state.currentProject.id, state.currentProject.name)\n        set({ isSaving: true, error: null })\n\n        try {\n          const response = await fetch(`/api/projects/${state.currentProject.id}`, {\n            method: 'PUT',\n            headers: {\n              'Content-Type': 'application/json',\n            },\n            body: JSON.stringify({\n              name: state.currentProject.name,\n              description: state.currentProject.description,\n              webhook_url: state.currentProject.webhook_url,\n              components: state.currentProject.components,\n            }),\n          })\n\n          if (!response.ok) {\n            const errorData = await response.json()\n            console.error('Save project error:', errorData)\n            throw new Error(errorData.error || 'Failed to save project')\n          }\n\n          const { project } = await response.json()\n          set({\n            currentProject: project,\n            isSaving: false,\n            lastSaved: new Date().toISOString()\n          })\n\n          // Show success toast\n          if (typeof window !== 'undefined' && (window as any).addToast) {\n            ;(window as any).addToast({\n              type: 'success',\n              title: 'Project saved',\n              description: 'Your changes have been saved successfully'\n            })\n          }\n        } catch (error) {\n          console.error('Error saving project:', error)\n          const errorMessage = error instanceof Error ? error.message : 'Failed to save project'\n          set({\n            error: errorMessage,\n            isSaving: false\n          })\n\n          // Show error toast\n          if (typeof window !== 'undefined' && (window as any).addToast) {\n            ;(window as any).addToast({\n              type: 'error',\n              title: 'Save failed',\n              description: errorMessage\n            })\n          }\n        }\n      },\n\n      loadProject: async (projectId: string) => {\n        set({ isLoading: true, error: null })\n\n        try {\n          const response = await fetch(`/api/projects/${projectId}`)\n\n          if (!response.ok) {\n            const errorData = await response.json()\n            throw new Error(errorData.error || 'Failed to load project')\n          }\n\n          const { project } = await response.json()\n          set({ currentProject: project, isLoading: false })\n        } catch (error) {\n          console.error('Error loading project:', error)\n          set({\n            error: error instanceof Error ? error.message : 'Failed to load project',\n            isLoading: false\n          })\n        }\n      },\n\n      createProject: async (name: string, description?: string) => {\n        console.log('Creating new project:', name)\n        set({ isSaving: true, error: null })\n\n        try {\n          const response = await fetch('/api/projects', {\n            method: 'POST',\n            headers: {\n              'Content-Type': 'application/json',\n            },\n            body: JSON.stringify({\n              name,\n              description,\n              components: [],\n            }),\n          })\n\n          if (!response.ok) {\n            const errorData = await response.json()\n            console.error('Create project error:', errorData)\n            throw new Error(errorData.error || 'Failed to create project')\n          }\n\n          const { project } = await response.json()\n          set({ currentProject: project, isSaving: false })\n        } catch (error) {\n          console.error('Error creating project:', error)\n          const errorMessage = error instanceof Error ? error.message : 'Failed to create project'\n\n          // If it's an organization error, provide helpful guidance\n          if (errorMessage.includes('No organization found')) {\n            set({\n              error: 'Setup required: Please refresh the page and try again. If the problem persists, contact support.',\n              isSaving: false\n            })\n\n            // Show error toast with more details\n            if (typeof window !== 'undefined' && (window as any).addToast) {\n              ;(window as any).addToast({\n                type: 'error',\n                title: 'Account setup required',\n                description: 'Please refresh the page and try again. Your account needs to be properly initialized.'\n              })\n            }\n          } else {\n            set({\n              error: errorMessage,\n              isSaving: false\n            })\n\n            // Show error toast\n            if (typeof window !== 'undefined' && (window as any).addToast) {\n              ;(window as any).addToast({\n                type: 'error',\n                title: 'Create project failed',\n                description: errorMessage\n              })\n            }\n          }\n        }\n      },\n    }),\n    { name: 'ui-builder-store' }\n  )\n)\n\n// API Testing Store\ninterface APIRequest {\n  id: string\n  name: string\n  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'\n  url: string\n  headers: Record<string, string>\n  body?: string\n}\n\ninterface APITestingState {\n  requests: APIRequest[]\n  currentRequest: APIRequest | null\n  isLoading: boolean\n  lastResponse: any\n  addRequest: (request: APIRequest) => void\n  updateRequest: (requestId: string, updates: Partial<APIRequest>) => void\n  removeRequest: (requestId: string) => void\n  setCurrentRequest: (request: APIRequest | null) => void\n  setLoading: (loading: boolean) => void\n  setLastResponse: (response: any) => void\n}\n\nexport const useAPITestingStore = create<APITestingState>()(\n  devtools(\n    persist(\n      (set) => ({\n        requests: [],\n        currentRequest: null,\n        isLoading: false,\n        lastResponse: null,\n        addRequest: (request) =>\n          set((state) => ({ requests: [...state.requests, request] })),\n        updateRequest: (requestId, updates) =>\n          set((state) => ({\n            requests: state.requests.map((req) =>\n              req.id === requestId ? { ...req, ...updates } : req\n            ),\n          })),\n        removeRequest: (requestId) =>\n          set((state) => ({\n            requests: state.requests.filter((req) => req.id !== requestId),\n          })),\n        setCurrentRequest: (request) => set({ currentRequest: request }),\n        setLoading: (isLoading) => set({ isLoading }),\n        setLastResponse: (lastResponse) => set({ lastResponse }),\n      }),\n      {\n        name: 'api-testing-storage',\n        partialize: (state) => ({ requests: state.requests }),\n      }\n    ),\n    { name: 'api-testing-store' }\n  )\n)\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;;;;AAUA,0BAA0B;AAC1B,MAAM;IAMJ,UAAU;QACR,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,YAAY,CAAC,IAAI,CAAC,SAAS;IACnD;IAEA,OAAO;QACL,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,eAAe,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;IACzD;IAEA,IAAI,cAAc;QAChB,OAAO,AAAC,OAA0B,OAApB,IAAI,CAAC,SAAS,CAAC,IAAI,EAAC;IACpC;IAfA,YACE,AAAQ,SAAsB,EAC9B,AAAQ,KAAU,CAClB;;;aAFQ,YAAA;aACA,QAAA;IACP;AAaL;AAEA,MAAM;IAMJ,UAAU;QACR,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,eAAe,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;IACzD;IAEA,OAAO;QACL,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,YAAY,CAAC,IAAI,CAAC,SAAS;IACnD;IAEA,IAAI,cAAc;QAChB,OAAO,AAAC,UAA6B,OAApB,IAAI,CAAC,SAAS,CAAC,IAAI,EAAC;IACvC;IAfA,YACE,AAAQ,SAAsB,EAC9B,AAAQ,KAAU,CAClB;;;aAFQ,YAAA;aACA,QAAA;IACP;AAaL;AAEA,MAAM;IAQJ,UAAU;QACR,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,eAAe,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,QAAQ;IACvE;IAEA,OAAO;QACL,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,eAAe,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,QAAQ;IACvE;IAEA,IAAI,cAAc;QAChB,OAAO,AAAC,oBAAoC,OAAjB,IAAI,CAAC,WAAW;IAC7C;IAjBA,YACE,AAAQ,WAAmB,EAC3B,AAAQ,QAA8B,EACtC,AAAQ,QAA8B,EACtC,AAAQ,KAAU,CAClB;;;;;aAJQ,cAAA;aACA,WAAA;aACA,WAAA;aACA,QAAA;IACP;AAaL;AAiBO,MAAM,eAAe,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,IAC/B,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD,EACL,CAAC,MAAQ,CAAC;QACR,MAAM;QACN,WAAW;QACX,SAAS,CAAC,OAAS,IAAI;gBAAE;YAAK;QAC9B,YAAY,CAAC,YAAc,IAAI;gBAAE;YAAU;QAC3C,QAAQ,IAAM,IAAI;gBAAE,MAAM;YAAK;IACjC,CAAC,GACD;IAAE,MAAM;AAAa;AA6DlB,MAAM,oBAAoB,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,IACpC,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD,EACL,CAAC,KAAK,MAAQ,CAAC;QACb,gBAAgB;QAChB,mBAAmB;QACnB,kBAAkB;QAClB,eAAe;QACf,UAAU;QACV,WAAW;QACX,OAAO;QACP,UAAU;QACV,WAAW;QACX,SAAS,EAAE;QACX,cAAc,CAAC;QACf,gBAAgB;QAChB,mBAAmB,CAAC,UAAY,IAAI;gBAAE,gBAAgB;YAAQ;QAC9D,sBAAsB,CAAC,cAAgB,IAAI;gBAAE,mBAAmB;YAAY;QAC5E,qBAAqB,CAAC,YAAc,IAAI;gBAAE,kBAAkB;YAAU;QACtE,mBAAmB,IAAM,IAAI,CAAC,QAAU,CAAC;oBAAE,eAAe,CAAC,MAAM,aAAa;gBAAC,CAAC;QAChF,UAAU,CAAC,QAAU,IAAI;gBAAE;YAAM;QACjC,YAAY,CAAC,YAAc,IAAI;gBAAE;YAAU;QAC3C,WAAW,CAAC,WAAa,IAAI;gBAAE;YAAS;QACxC,aAAa,CAAC,WAAa,IAAI;gBAAE;YAAS;QAE1C,iBAAiB;YACf,MAAM,QAAQ;YACd,IAAI,MAAM,QAAQ,IAAI,MAAM,cAAc,IAAI,MAAM,cAAc,CAAC,EAAE,KAAK,gBAAgB;gBACxF,gDAAgD;gBAChD,WAAW;oBACT,MAAM,eAAe;oBACrB,IAAI,aAAa,cAAc,IAAI,CAAC,aAAa,QAAQ,EAAE;wBACzD,aAAa,WAAW;oBAC1B;gBACF,GAAG;YACL;QACF;QAEA,iCAAiC;QACjC,gBAAgB,CAAC;YACf,MAAM,QAAQ;YAEd,sBAAsB;YACtB,QAAQ,OAAO;YAEf,iBAAiB;YACjB,MAAM,aAAa,MAAM,OAAO,CAAC,KAAK,CAAC,GAAG,MAAM,YAAY,GAAG;YAC/D,WAAW,IAAI,CAAC;YAEhB,qBAAqB;YACrB,IAAI,WAAW,MAAM,GAAG,MAAM,cAAc,EAAE;gBAC5C,WAAW,KAAK;YAClB;YAEA,IAAI;gBACF,SAAS;gBACT,cAAc,WAAW,MAAM,GAAG;YACpC;QACF;QAEA,MAAM;YACJ,MAAM,QAAQ;YACd,IAAI,MAAM,YAAY,IAAI,GAAG;gBAC3B,MAAM,UAAU,MAAM,OAAO,CAAC,MAAM,YAAY,CAAC;gBACjD,QAAQ,IAAI;gBACZ,IAAI;oBAAE,cAAc,MAAM,YAAY,GAAG;gBAAE;YAC7C;QACF;QAEA,MAAM;YACJ,MAAM,QAAQ;YACd,IAAI,MAAM,YAAY,GAAG,MAAM,OAAO,CAAC,MAAM,GAAG,GAAG;gBACjD,MAAM,UAAU,MAAM,OAAO,CAAC,MAAM,YAAY,GAAG,EAAE;gBACrD,QAAQ,OAAO;gBACf,IAAI;oBAAE,cAAc,MAAM,YAAY,GAAG;gBAAE;YAC7C;QACF;QAEA,SAAS;YACP,MAAM,QAAQ;YACd,OAAO,MAAM,YAAY,IAAI;QAC/B;QAEA,SAAS;YACP,MAAM,QAAQ;YACd,OAAO,MAAM,YAAY,GAAG,MAAM,OAAO,CAAC,MAAM,GAAG;QACrD;QAEA,cAAc;YACZ,IAAI;gBAAE,SAAS,EAAE;gBAAE,cAAc,CAAC;YAAE;QACtC;QACA,cAAc,CAAC;YACb,IAAI,CAAC;gBACH,IAAI,CAAC,MAAM,cAAc,EAAE,OAAO;gBAClC,OAAO;oBACL,gBAAgB;wBACd,GAAG,MAAM,cAAc;wBACvB,YAAY;+BAAI,MAAM,cAAc,CAAC,UAAU;4BAAE;yBAAU;oBAC7D;gBACF;YACF;YACA,2CAA2C;YAC3C,MAAM,eAAe;QACvB;QAEA,yCAAyC;QACzC,yBAAyB,CAAC;YACxB,MAAM,UAAU,IAAI,oBAAoB,WAAW;gBAAE,UAAU;YAAI;YACnE,MAAM,cAAc,CAAC;YACrB,MAAM,eAAe;QACvB;QAEA,4BAA4B,CAAC;gBAET;YADlB,MAAM,QAAQ;YACd,MAAM,aAAY,wBAAA,MAAM,cAAc,cAApB,4CAAA,sBAAsB,UAAU,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;YACtE,IAAI,WAAW;gBACb,MAAM,UAAU,IAAI,uBAAuB,WAAW;oBAAE,UAAU;gBAAI;gBACtE,MAAM,cAAc,CAAC;gBACrB,MAAM,eAAe;YACvB;QACF;QAEA,4BAA4B,CAAC,aAAa;gBAEtB;YADlB,MAAM,QAAQ;YACd,MAAM,aAAY,wBAAA,MAAM,cAAc,cAApB,4CAAA,sBAAsB,UAAU,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;YACtE,IAAI,WAAW;gBACb,MAAM,WAAW;oBAAE,GAAG,SAAS;gBAAC;gBAChC,MAAM,WAAW;oBAAE,GAAG,SAAS;oBAAE,GAAG,OAAO;gBAAC;gBAC5C,MAAM,UAAU,IAAI,uBAAuB,aAAa,UAAU,UAAU;oBAAE,UAAU;gBAAI;gBAC5F,MAAM,cAAc,CAAC;gBACrB,MAAM,eAAe;YACvB;QACF;QACA,iBAAiB,CAAC,aAAa;YAC7B,IAAI,CAAC;gBACH,IAAI,CAAC,MAAM,cAAc,EAAE,OAAO;gBAClC,OAAO;oBACL,gBAAgB;wBACd,GAAG,MAAM,cAAc;wBACvB,YAAY,MAAM,cAAc,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,OAC/C,KAAK,EAAE,KAAK,cAAc;gCAAE,GAAG,IAAI;gCAAE,GAAG,OAAO;4BAAC,IAAI;oBAExD;gBACF;YACF;YACA,6CAA6C;YAC7C,MAAM,eAAe;QACvB;QACA,iBAAiB,CAAC;YAChB,IAAI,CAAC;gBACH,IAAI,CAAC,MAAM,cAAc,EAAE,OAAO;gBAClC,OAAO;oBACL,gBAAgB;wBACd,GAAG,MAAM,cAAc;wBACvB,YAAY,MAAM,cAAc,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,OAAS,KAAK,EAAE,KAAK;oBAC3E;gBACF;YACF;YACA,6CAA6C;YAC7C,MAAM,eAAe;QACvB;QAEA,+BAA+B;QAC/B,aAAa;YACX,MAAM,QAAQ;YACd,IAAI,CAAC,MAAM,cAAc,EAAE;gBACzB,QAAQ,GAAG,CAAC;gBACZ;YACF;YAEA,QAAQ,GAAG,CAAC,mBAAmB,MAAM,cAAc,CAAC,EAAE,EAAE,MAAM,cAAc,CAAC,IAAI;YACjF,IAAI;gBAAE,UAAU;gBAAM,OAAO;YAAK;YAElC,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM,AAAC,iBAAwC,OAAxB,MAAM,cAAc,CAAC,EAAE,GAAI;oBACvE,QAAQ;oBACR,SAAS;wBACP,gBAAgB;oBAClB;oBACA,MAAM,KAAK,SAAS,CAAC;wBACnB,MAAM,MAAM,cAAc,CAAC,IAAI;wBAC/B,aAAa,MAAM,cAAc,CAAC,WAAW;wBAC7C,aAAa,MAAM,cAAc,CAAC,WAAW;wBAC7C,YAAY,MAAM,cAAc,CAAC,UAAU;oBAC7C;gBACF;gBAEA,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;oBACrC,QAAQ,KAAK,CAAC,uBAAuB;oBACrC,MAAM,IAAI,MAAM,UAAU,KAAK,IAAI;gBACrC;gBAEA,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM,SAAS,IAAI;gBACvC,IAAI;oBACF,gBAAgB;oBAChB,UAAU;oBACV,WAAW,IAAI,OAAO,WAAW;gBACnC;gBAEA,qBAAqB;gBACrB,IAAI,aAAkB,eAAe,AAAC,OAAe,QAAQ,EAAE;;oBAC3D,OAAe,QAAQ,CAAC;wBACxB,MAAM;wBACN,OAAO;wBACP,aAAa;oBACf;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,yBAAyB;gBACvC,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D,IAAI;oBACF,OAAO;oBACP,UAAU;gBACZ;gBAEA,mBAAmB;gBACnB,IAAI,aAAkB,eAAe,AAAC,OAAe,QAAQ,EAAE;;oBAC3D,OAAe,QAAQ,CAAC;wBACxB,MAAM;wBACN,OAAO;wBACP,aAAa;oBACf;gBACF;YACF;QACF;QAEA,aAAa,OAAO;YAClB,IAAI;gBAAE,WAAW;gBAAM,OAAO;YAAK;YAEnC,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM,AAAC,iBAA0B,OAAV;gBAE9C,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;oBACrC,MAAM,IAAI,MAAM,UAAU,KAAK,IAAI;gBACrC;gBAEA,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM,SAAS,IAAI;gBACvC,IAAI;oBAAE,gBAAgB;oBAAS,WAAW;gBAAM;YAClD,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,0BAA0B;gBACxC,IAAI;oBACF,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;oBAChD,WAAW;gBACb;YACF;QACF;QAEA,eAAe,OAAO,MAAc;YAClC,QAAQ,GAAG,CAAC,yBAAyB;YACrC,IAAI;gBAAE,UAAU;gBAAM,OAAO;YAAK;YAElC,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM,iBAAiB;oBAC5C,QAAQ;oBACR,SAAS;wBACP,gBAAgB;oBAClB;oBACA,MAAM,KAAK,SAAS,CAAC;wBACnB;wBACA;wBACA,YAAY,EAAE;oBAChB;gBACF;gBAEA,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;oBACrC,QAAQ,KAAK,CAAC,yBAAyB;oBACvC,MAAM,IAAI,MAAM,UAAU,KAAK,IAAI;gBACrC;gBAEA,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM,SAAS,IAAI;gBACvC,IAAI;oBAAE,gBAAgB;oBAAS,UAAU;gBAAM;YACjD,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,2BAA2B;gBACzC,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAE9D,0DAA0D;gBAC1D,IAAI,aAAa,QAAQ,CAAC,0BAA0B;oBAClD,IAAI;wBACF,OAAO;wBACP,UAAU;oBACZ;oBAEA,qCAAqC;oBACrC,IAAI,aAAkB,eAAe,AAAC,OAAe,QAAQ,EAAE;;wBAC3D,OAAe,QAAQ,CAAC;4BACxB,MAAM;4BACN,OAAO;4BACP,aAAa;wBACf;oBACF;gBACF,OAAO;oBACL,IAAI;wBACF,OAAO;wBACP,UAAU;oBACZ;oBAEA,mBAAmB;oBACnB,IAAI,aAAkB,eAAe,AAAC,OAAe,QAAQ,EAAE;;wBAC3D,OAAe,QAAQ,CAAC;4BACxB,MAAM;4BACN,OAAO;4BACP,aAAa;wBACf;oBACF;gBACF;YACF;QACF;IACF,CAAC,GACD;IAAE,MAAM;AAAmB;AA2BxB,MAAM,qBAAqB,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,IACrC,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD,EACL,CAAA,GAAA,gJAAA,CAAA,UAAO,AAAD,EACJ,CAAC,MAAQ,CAAC;QACR,UAAU,EAAE;QACZ,gBAAgB;QAChB,WAAW;QACX,cAAc;QACd,YAAY,CAAC,UACX,IAAI,CAAC,QAAU,CAAC;oBAAE,UAAU;2BAAI,MAAM,QAAQ;wBAAE;qBAAQ;gBAAC,CAAC;QAC5D,eAAe,CAAC,WAAW,UACzB,IAAI,CAAC,QAAU,CAAC;oBACd,UAAU,MAAM,QAAQ,CAAC,GAAG,CAAC,CAAC,MAC5B,IAAI,EAAE,KAAK,YAAY;4BAAE,GAAG,GAAG;4BAAE,GAAG,OAAO;wBAAC,IAAI;gBAEpD,CAAC;QACH,eAAe,CAAC,YACd,IAAI,CAAC,QAAU,CAAC;oBACd,UAAU,MAAM,QAAQ,CAAC,MAAM,CAAC,CAAC,MAAQ,IAAI,EAAE,KAAK;gBACtD,CAAC;QACH,mBAAmB,CAAC,UAAY,IAAI;gBAAE,gBAAgB;YAAQ;QAC9D,YAAY,CAAC,YAAc,IAAI;gBAAE;YAAU;QAC3C,iBAAiB,CAAC,eAAiB,IAAI;gBAAE;YAAa;IACxD,CAAC,GACD;IACE,MAAM;IACN,YAAY,CAAC,QAAU,CAAC;YAAE,UAAU,MAAM,QAAQ;QAAC,CAAC;AACtD,IAEF;IAAE,MAAM;AAAoB", "debugId": null}}, {"offset": {"line": 497, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/FlowUi/src/hooks/useAuth.ts"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { createClientComponentClient } from '@/lib/supabase'\nimport { useAuthStore } from '@/lib/store'\nimport { User } from '@/types'\n\nexport function useAuth() {\n  const router = useRouter()\n  const supabase = createClientComponentClient()\n  const { user, isLoading, setUser, setLoading } = useAuthStore()\n  const [isHydrated, setIsHydrated] = useState(false)\n\n  useEffect(() => {\n    // Set hydrated flag\n    setIsHydrated(true)\n\n    // Get initial session\n    const getInitialSession = async () => {\n      try {\n        console.log('🔍 Getting initial session...')\n        const { data: { session }, error } = await supabase.auth.getSession()\n\n        if (error) {\n          console.error('❌ Error getting session:', error)\n          setUser(null)\n        } else if (session?.user) {\n          console.log('✅ Session found, setting user:', session.user.email)\n          // For now, create user from session data (database setup not required)\n          setUser({\n            id: session.user.id,\n            email: session.user.email!,\n            subscription_tier: 'free',\n            created_at: session.user.created_at || new Date().toISOString(),\n            updated_at: new Date().toISOString(),\n          })\n        } else {\n          console.log('ℹ️ No session found')\n          setUser(null)\n        }\n      } catch (error) {\n        console.error('❌ Error in getInitialSession:', error)\n        setUser(null)\n      } finally {\n        console.log('🏁 Setting loading to false')\n        setLoading(false)\n      }\n    }\n\n    getInitialSession()\n\n    // Listen for auth changes\n    const { data: { subscription } } = supabase.auth.onAuthStateChange(\n      async (event, session) => {\n        if (event === 'SIGNED_IN' && session?.user) {\n          // Handle sign in - create user from session data\n          setUser({\n            id: session.user.id,\n            email: session.user.email!,\n            subscription_tier: 'free',\n            created_at: session.user.created_at || new Date().toISOString(),\n            updated_at: new Date().toISOString(),\n          })\n          router.push('/dashboard')\n        } else if (event === 'SIGNED_OUT') {\n          // Handle sign out\n          setUser(null)\n          router.push('/')\n        }\n      }\n    )\n\n    return () => {\n      subscription.unsubscribe()\n    }\n  }, [supabase, setUser, setLoading, router])\n\n  const signIn = async (email: string, password: string) => {\n    try {\n      setLoading(true)\n      const { error } = await supabase.auth.signInWithPassword({\n        email,\n        password,\n      })\n\n      if (error) throw error\n      \n      return { success: true }\n    } catch (error: any) {\n      return { \n        success: false, \n        error: error.message || 'An error occurred during sign in' \n      }\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const signUp = async (email: string, password: string) => {\n    try {\n      setLoading(true)\n      const { error } = await supabase.auth.signUp({\n        email,\n        password,\n        options: {\n          emailRedirectTo: `${window.location.origin}/auth/callback`,\n        },\n      })\n\n      if (error) throw error\n      \n      return { success: true }\n    } catch (error: any) {\n      return { \n        success: false, \n        error: error.message || 'An error occurred during sign up' \n      }\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const signOut = async () => {\n    try {\n      setLoading(true)\n      const { error } = await supabase.auth.signOut()\n      if (error) throw error\n      \n      return { success: true }\n    } catch (error: any) {\n      return { \n        success: false, \n        error: error.message || 'An error occurred during sign out' \n      }\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const signInWithProvider = async (provider: 'google' | 'github') => {\n    try {\n      setLoading(true)\n      const { error } = await supabase.auth.signInWithOAuth({\n        provider,\n        options: {\n          redirectTo: `${window.location.origin}/auth/callback`,\n        },\n      })\n\n      if (error) throw error\n      \n      return { success: true }\n    } catch (error: any) {\n      return { \n        success: false, \n        error: error.message || 'An error occurred during OAuth sign in' \n      }\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const resetPassword = async (email: string) => {\n    try {\n      setLoading(true)\n      const { error } = await supabase.auth.resetPasswordForEmail(email, {\n        redirectTo: `${window.location.origin}/auth/reset-password`,\n      })\n\n      if (error) throw error\n      \n      return { success: true }\n    } catch (error: any) {\n      return { \n        success: false, \n        error: error.message || 'An error occurred during password reset' \n      }\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  return {\n    user,\n    isLoading: isLoading || !isHydrated,\n    signIn,\n    signUp,\n    signOut,\n    signInWithProvider,\n    resetPassword,\n    isAuthenticated: isHydrated && !!user,\n  }\n}\n"], "names": [], "mappings": ";;;AAEA;AACA;AACA;AACA;;AALA;;;;;AAQO,SAAS;;IACd,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,yHAAA,CAAA,8BAA2B,AAAD;IAC3C,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD;IAC5D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6BAAE;YACR,oBAAoB;YACpB,cAAc;YAEd,sBAAsB;YACtB,MAAM;uDAAoB;oBACxB,IAAI;wBACF,QAAQ,GAAG,CAAC;wBACZ,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU;wBAEnE,IAAI,OAAO;4BACT,QAAQ,KAAK,CAAC,4BAA4B;4BAC1C,QAAQ;wBACV,OAAO,IAAI,oBAAA,8BAAA,QAAS,IAAI,EAAE;4BACxB,QAAQ,GAAG,CAAC,kCAAkC,QAAQ,IAAI,CAAC,KAAK;4BAChE,uEAAuE;4BACvE,QAAQ;gCACN,IAAI,QAAQ,IAAI,CAAC,EAAE;gCACnB,OAAO,QAAQ,IAAI,CAAC,KAAK;gCACzB,mBAAmB;gCACnB,YAAY,QAAQ,IAAI,CAAC,UAAU,IAAI,IAAI,OAAO,WAAW;gCAC7D,YAAY,IAAI,OAAO,WAAW;4BACpC;wBACF,OAAO;4BACL,QAAQ,GAAG,CAAC;4BACZ,QAAQ;wBACV;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,iCAAiC;wBAC/C,QAAQ;oBACV,SAAU;wBACR,QAAQ,GAAG,CAAC;wBACZ,WAAW;oBACb;gBACF;;YAEA;YAEA,0BAA0B;YAC1B,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,EAAE,GAAG,SAAS,IAAI,CAAC,iBAAiB;qCAChE,OAAO,OAAO;oBACZ,IAAI,UAAU,gBAAe,oBAAA,8BAAA,QAAS,IAAI,GAAE;wBAC1C,iDAAiD;wBACjD,QAAQ;4BACN,IAAI,QAAQ,IAAI,CAAC,EAAE;4BACnB,OAAO,QAAQ,IAAI,CAAC,KAAK;4BACzB,mBAAmB;4BACnB,YAAY,QAAQ,IAAI,CAAC,UAAU,IAAI,IAAI,OAAO,WAAW;4BAC7D,YAAY,IAAI,OAAO,WAAW;wBACpC;wBACA,OAAO,IAAI,CAAC;oBACd,OAAO,IAAI,UAAU,cAAc;wBACjC,kBAAkB;wBAClB,QAAQ;wBACR,OAAO,IAAI,CAAC;oBACd;gBACF;;YAGF;qCAAO;oBACL,aAAa,WAAW;gBAC1B;;QACF;4BAAG;QAAC;QAAU;QAAS;QAAY;KAAO;IAE1C,MAAM,SAAS,OAAO,OAAe;QACnC,IAAI;YACF,WAAW;YACX,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,kBAAkB,CAAC;gBACvD;gBACA;YACF;YAEA,IAAI,OAAO,MAAM;YAEjB,OAAO;gBAAE,SAAS;YAAK;QACzB,EAAE,OAAO,OAAY;YACnB,OAAO;gBACL,SAAS;gBACT,OAAO,MAAM,OAAO,IAAI;YAC1B;QACF,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,SAAS,OAAO,OAAe;QACnC,IAAI;YACF,WAAW;YACX,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,MAAM,CAAC;gBAC3C;gBACA;gBACA,SAAS;oBACP,iBAAiB,AAAC,GAAyB,OAAvB,OAAO,QAAQ,CAAC,MAAM,EAAC;gBAC7C;YACF;YAEA,IAAI,OAAO,MAAM;YAEjB,OAAO;gBAAE,SAAS;YAAK;QACzB,EAAE,OAAO,OAAY;YACnB,OAAO;gBACL,SAAS;gBACT,OAAO,MAAM,OAAO,IAAI;YAC1B;QACF,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,UAAU;QACd,IAAI;YACF,WAAW;YACX,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;YAC7C,IAAI,OAAO,MAAM;YAEjB,OAAO;gBAAE,SAAS;YAAK;QACzB,EAAE,OAAO,OAAY;YACnB,OAAO;gBACL,SAAS;gBACT,OAAO,MAAM,OAAO,IAAI;YAC1B;QACF,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,qBAAqB,OAAO;QAChC,IAAI;YACF,WAAW;YACX,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,eAAe,CAAC;gBACpD;gBACA,SAAS;oBACP,YAAY,AAAC,GAAyB,OAAvB,OAAO,QAAQ,CAAC,MAAM,EAAC;gBACxC;YACF;YAEA,IAAI,OAAO,MAAM;YAEjB,OAAO;gBAAE,SAAS;YAAK;QACzB,EAAE,OAAO,OAAY;YACnB,OAAO;gBACL,SAAS;gBACT,OAAO,MAAM,OAAO,IAAI;YAC1B;QACF,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,gBAAgB,OAAO;QAC3B,IAAI;YACF,WAAW;YACX,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,qBAAqB,CAAC,OAAO;gBACjE,YAAY,AAAC,GAAyB,OAAvB,OAAO,QAAQ,CAAC,MAAM,EAAC;YACxC;YAEA,IAAI,OAAO,MAAM;YAEjB,OAAO;gBAAE,SAAS;YAAK;QACzB,EAAE,OAAO,OAAY;YACnB,OAAO;gBACL,SAAS;gBACT,OAAO,MAAM,OAAO,IAAI;YAC1B;QACF,SAAU;YACR,WAAW;QACb;IACF;IAEA,OAAO;QACL;QACA,WAAW,aAAa,CAAC;QACzB;QACA;QACA;QACA;QACA;QACA,iBAAiB,cAAc,CAAC,CAAC;IACnC;AACF;GAzLgB;;QACC,qIAAA,CAAA,YAAS;QAEyB,sHAAA,CAAA,eAAY", "debugId": null}}, {"offset": {"line": 712, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/FlowUi/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function generateId(): string {\n  return Math.random().toString(36).substr(2, 9)\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAC9C", "debugId": null}}, {"offset": {"line": 738, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/FlowUi/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,KASb;QATa,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF,GATa;IAUd,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 802, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/FlowUi/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACZ,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACjB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACvB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACnB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 924, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/FlowUi/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,KAM6C;QAN7C,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD,GAN7C;IAOb,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAhBS", "debugId": null}}, {"offset": {"line": 977, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/FlowUi/src/components/builder/component-palette.tsx"], "sourcesContent": ["'use client'\n\nimport { useDraggable } from '@dnd-kit/core'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport { ComponentType } from '@/types'\nimport {\n  MousePointer2,\n  Type,\n  Heading1,\n  Square,\n  Image,\n  Table,\n  BarChart3,\n  Container,\n  Grid3X3,\n  Rows,\n  CheckSquare,\n  Circle,\n  Upload\n} from 'lucide-react'\n\ninterface ComponentItem {\n  type: ComponentType\n  label: string\n  icon: React.ReactNode\n  category: 'form' | 'display' | 'layout'\n  description: string\n}\n\nconst componentItems: ComponentItem[] = [\n  // Form Components\n  {\n    type: 'button',\n    label: 'Button',\n    icon: <MousePointer2 className=\"w-4 h-4\" />,\n    category: 'form',\n    description: 'Interactive button element'\n  },\n  {\n    type: 'input',\n    label: 'Input',\n    icon: <Type className=\"w-4 h-4\" />,\n    category: 'form',\n    description: 'Text input field'\n  },\n  {\n    type: 'textarea',\n    label: 'Textarea',\n    icon: <Square className=\"w-4 h-4\" />,\n    category: 'form',\n    description: 'Multi-line text input'\n  },\n  {\n    type: 'select',\n    label: 'Select',\n    icon: <Square className=\"w-4 h-4\" />,\n    category: 'form',\n    description: 'Dropdown selection'\n  },\n  {\n    type: 'checkbox',\n    label: 'Checkbox',\n    icon: <CheckSquare className=\"w-4 h-4\" />,\n    category: 'form',\n    description: 'Checkbox input'\n  },\n  {\n    type: 'radio',\n    label: 'Radio',\n    icon: <Circle className=\"w-4 h-4\" />,\n    category: 'form',\n    description: 'Radio button group'\n  },\n  {\n    type: 'file-upload',\n    label: 'File Upload',\n    icon: <Upload className=\"w-4 h-4\" />,\n    category: 'form',\n    description: 'File upload component'\n  },\n\n  // Display Components\n  {\n    type: 'text',\n    label: 'Text',\n    icon: <Type className=\"w-4 h-4\" />,\n    category: 'display',\n    description: 'Plain text content'\n  },\n  {\n    type: 'heading',\n    label: 'Heading',\n    icon: <Heading1 className=\"w-4 h-4\" />,\n    category: 'display',\n    description: 'Heading text (H1-H6)'\n  },\n  {\n    type: 'image',\n    label: 'Image',\n    icon: <Image className=\"w-4 h-4\" />,\n    category: 'display',\n    description: 'Image display'\n  },\n  {\n    type: 'card',\n    label: 'Card',\n    icon: <Square className=\"w-4 h-4\" />,\n    category: 'display',\n    description: 'Card container'\n  },\n  {\n    type: 'table',\n    label: 'Table',\n    icon: <Table className=\"w-4 h-4\" />,\n    category: 'display',\n    description: 'Data table'\n  },\n  {\n    type: 'chart',\n    label: 'Chart',\n    icon: <BarChart3 className=\"w-4 h-4\" />,\n    category: 'display',\n    description: 'Data visualization'\n  },\n\n  // Layout Components\n  {\n    type: 'container',\n    label: 'Container',\n    icon: <Container className=\"w-4 h-4\" />,\n    category: 'layout',\n    description: 'Generic container'\n  },\n  {\n    type: 'grid',\n    label: 'Grid',\n    icon: <Grid3X3 className=\"w-4 h-4\" />,\n    category: 'layout',\n    description: 'CSS Grid layout'\n  },\n  {\n    type: 'flex',\n    label: 'Flex',\n    icon: <Rows className=\"w-4 h-4\" />,\n    category: 'layout',\n    description: 'Flexbox layout'\n  },\n]\n\ninterface DraggableComponentProps {\n  item: ComponentItem\n}\n\nfunction DraggableComponent({ item }: DraggableComponentProps) {\n  const { attributes, listeners, setNodeRef, transform, isDragging } = useDraggable({\n    id: `palette-${item.type}`,\n    data: {\n      fromPalette: true,\n      componentType: item.type,\n    },\n  })\n\n  const style = transform ? {\n    transform: `translate3d(${transform.x}px, ${transform.y}px, 0)`,\n  } : undefined\n\n  return (\n    <div\n      ref={setNodeRef}\n      style={style}\n      {...listeners}\n      {...attributes}\n      className={`\n        p-3 border border-gray-200 dark:border-gray-600 rounded-lg cursor-grab\n        hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\n        ${isDragging ? 'opacity-50' : ''}\n      `}\n    >\n      <div className=\"flex items-center gap-2 mb-1\">\n        {item.icon}\n        <span className=\"font-medium text-sm\">{item.label}</span>\n      </div>\n      <p className=\"text-xs text-muted-foreground\">{item.description}</p>\n    </div>\n  )\n}\n\nexport function ComponentPalette() {\n  const categories = ['form', 'display', 'layout'] as const\n\n  return (\n    <div className=\"p-4\">\n      <h2 className=\"text-lg font-semibold mb-4\">Components</h2>\n      \n      {categories.map((category) => (\n        <div key={category} className=\"mb-6\">\n          <h3 className=\"text-sm font-medium text-muted-foreground uppercase tracking-wide mb-3\">\n            {category} Components\n          </h3>\n          <div className=\"space-y-2\">\n            {componentItems\n              .filter((item) => item.category === category)\n              .map((item) => (\n                <DraggableComponent key={item.type} item={item} />\n              ))}\n          </div>\n        </div>\n      ))}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAIA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AANA;;;AA8BA,MAAM,iBAAkC;IACtC,kBAAkB;IAClB;QACE,MAAM;QACN,OAAO;QACP,oBAAM,6LAAC,+NAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;QAC/B,UAAU;QACV,aAAa;IACf;IACA;QACE,MAAM;QACN,OAAO;QACP,oBAAM,6LAAC,qMAAA,CAAA,OAAI;YAAC,WAAU;;;;;;QACtB,UAAU;QACV,aAAa;IACf;IACA;QACE,MAAM;QACN,OAAO;QACP,oBAAM,6LAAC,yMAAA,CAAA,SAAM;YAAC,WAAU;;;;;;QACxB,UAAU;QACV,aAAa;IACf;IACA;QACE,MAAM;QACN,OAAO;QACP,oBAAM,6LAAC,yMAAA,CAAA,SAAM;YAAC,WAAU;;;;;;QACxB,UAAU;QACV,aAAa;IACf;IACA;QACE,MAAM;QACN,OAAO;QACP,oBAAM,6LAAC,8NAAA,CAAA,cAAW;YAAC,WAAU;;;;;;QAC7B,UAAU;QACV,aAAa;IACf;IACA;QACE,MAAM;QACN,OAAO;QACP,oBAAM,6LAAC,yMAAA,CAAA,SAAM;YAAC,WAAU;;;;;;QACxB,UAAU;QACV,aAAa;IACf;IACA;QACE,MAAM;QACN,OAAO;QACP,oBAAM,6LAAC,yMAAA,CAAA,SAAM;YAAC,WAAU;;;;;;QACxB,UAAU;QACV,aAAa;IACf;IAEA,qBAAqB;IACrB;QACE,MAAM;QACN,OAAO;QACP,oBAAM,6LAAC,qMAAA,CAAA,OAAI;YAAC,WAAU;;;;;;QACtB,UAAU;QACV,aAAa;IACf;IACA;QACE,MAAM;QACN,OAAO;QACP,oBAAM,6LAAC,iNAAA,CAAA,WAAQ;YAAC,WAAU;;;;;;QAC1B,UAAU;QACV,aAAa;IACf;IACA;QACE,MAAM;QACN,OAAO;QACP,oBAAM,6LAAC,uMAAA,CAAA,QAAK;YAAC,WAAU;;;;;;QACvB,UAAU;QACV,aAAa;IACf;IACA;QACE,MAAM;QACN,OAAO;QACP,oBAAM,6LAAC,yMAAA,CAAA,SAAM;YAAC,WAAU;;;;;;QACxB,UAAU;QACV,aAAa;IACf;IACA;QACE,MAAM;QACN,OAAO;QACP,oBAAM,6LAAC,uMAAA,CAAA,QAAK;YAAC,WAAU;;;;;;QACvB,UAAU;QACV,aAAa;IACf;IACA;QACE,MAAM;QACN,OAAO;QACP,oBAAM,6LAAC,qNAAA,CAAA,YAAS;YAAC,WAAU;;;;;;QAC3B,UAAU;QACV,aAAa;IACf;IAEA,oBAAoB;IACpB;QACE,MAAM;QACN,OAAO;QACP,oBAAM,6LAAC,+MAAA,CAAA,YAAS;YAAC,WAAU;;;;;;QAC3B,UAAU;QACV,aAAa;IACf;IACA;QACE,MAAM;QACN,OAAO;QACP,oBAAM,6LAAC,+MAAA,CAAA,UAAO;YAAC,WAAU;;;;;;QACzB,UAAU;QACV,aAAa;IACf;IACA;QACE,MAAM;QACN,OAAO;QACP,oBAAM,6LAAC,0MAAA,CAAA,OAAI;YAAC,WAAU;;;;;;QACtB,UAAU;QACV,aAAa;IACf;CACD;AAMD,SAAS,mBAAmB,KAAiC;QAAjC,EAAE,IAAI,EAA2B,GAAjC;;IAC1B,MAAM,EAAE,UAAU,EAAE,SAAS,EAAE,UAAU,EAAE,SAAS,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,8JAAA,CAAA,eAAY,AAAD,EAAE;QAChF,IAAI,AAAC,WAAoB,OAAV,KAAK,IAAI;QACxB,MAAM;YACJ,aAAa;YACb,eAAe,KAAK,IAAI;QAC1B;IACF;IAEA,MAAM,QAAQ,YAAY;QACxB,WAAW,AAAC,eAAgC,OAAlB,UAAU,CAAC,EAAC,QAAkB,OAAZ,UAAU,CAAC,EAAC;IAC1D,IAAI;IAEJ,qBACE,6LAAC;QACC,KAAK;QACL,OAAO;QACN,GAAG,SAAS;QACZ,GAAG,UAAU;QACd,WAAW,AAAC,gKAGuB,OAA/B,aAAa,eAAe,IAAG;;0BAGnC,6LAAC;gBAAI,WAAU;;oBACZ,KAAK,IAAI;kCACV,6LAAC;wBAAK,WAAU;kCAAuB,KAAK,KAAK;;;;;;;;;;;;0BAEnD,6LAAC;gBAAE,WAAU;0BAAiC,KAAK,WAAW;;;;;;;;;;;;AAGpE;GAhCS;;QAC8D,8JAAA,CAAA,eAAY;;;KAD1E;AAkCF,SAAS;IACd,MAAM,aAAa;QAAC;QAAQ;QAAW;KAAS;IAEhD,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAG,WAAU;0BAA6B;;;;;;YAE1C,WAAW,GAAG,CAAC,CAAC,yBACf,6LAAC;oBAAmB,WAAU;;sCAC5B,6LAAC;4BAAG,WAAU;;gCACX;gCAAS;;;;;;;sCAEZ,6LAAC;4BAAI,WAAU;sCACZ,eACE,MAAM,CAAC,CAAC,OAAS,KAAK,QAAQ,KAAK,UACnC,GAAG,CAAC,CAAC,qBACJ,6LAAC;oCAAmC,MAAM;mCAAjB,KAAK,IAAI;;;;;;;;;;;mBARhC;;;;;;;;;;;AAelB;MAvBgB", "debugId": null}}, {"offset": {"line": 1343, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/FlowUi/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,KAA4D;QAA5D,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC,GAA5D;IACb,qBACE,6LAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 1376, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/FlowUi/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\n  return (\n    <textarea\n      data-slot=\"textarea\"\n      className={cn(\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,KAAyD;QAAzD,EAAE,SAAS,EAAE,GAAG,OAAyC,GAAzD;IAChB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS", "debugId": null}}, {"offset": {"line": 1408, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/FlowUi/src/components/ui/checkbox.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as CheckboxPrimitive from \"@radix-ui/react-checkbox\"\nimport { Check } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Checkbox = React.forwardRef<\n  React.ElementRef<typeof CheckboxPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof CheckboxPrimitive.Root>\n>(({ className, ...props }, ref) => (\n  <CheckboxPrimitive.Root\n    ref={ref}\n    className={cn(\n      \"peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground\",\n      className\n    )}\n    {...props}\n  >\n    <CheckboxPrimitive.Indicator\n      className={cn(\"flex items-center justify-center text-current\")}\n    >\n      <Check className=\"h-4 w-4\" />\n    </CheckboxPrimitive.Indicator>\n  </CheckboxPrimitive.Root>\n))\nCheckbox.displayName = CheckboxPrimitive.Root.displayName\n\nexport { Checkbox }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,yBAAW,6JAAA,CAAA,aAAgB,MAG/B,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC,uKAAA,CAAA,OAAsB;QACrB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kTACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uKAAA,CAAA,YAA2B;YAC1B,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE;sBAEd,cAAA,6LAAC,uMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;;;;;;;;;;;;;AAIvB,SAAS,WAAW,GAAG,uKAAA,CAAA,OAAsB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1463, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/FlowUi/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,KAGoC;QAHpC,EACb,SAAS,EACT,GAAG,OAC8C,GAHpC;IAIb,qBACE,6LAAC,oKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 1498, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/FlowUi/src/components/builder/canvas-component.tsx"], "sourcesContent": ["'use client'\n\nimport { useDraggable } from '@dnd-kit/core'\nimport { UIComponent } from '@/types'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Textarea } from '@/components/ui/textarea'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport { Checkbox } from '@/components/ui/checkbox'\nimport { Label } from '@/components/ui/label'\nimport { useUIBuilderStore } from '@/lib/store'\n\ninterface CanvasComponentProps {\n  component: UIComponent\n  isSelected: boolean\n  onSelect: () => void\n}\n\nexport function CanvasComponent({ component, isSelected, onSelect }: CanvasComponentProps) {\n  const { updateComponent, removeComponent } = useUIBuilderStore()\n\n  const { attributes, listeners, setNodeRef, transform, isDragging } = useDraggable({\n    id: component.id,\n    data: {\n      component,\n      fromCanvas: true,\n    },\n  })\n\n  const style = {\n    position: 'absolute' as const,\n    left: component.position.x,\n    top: component.position.y,\n    width: component.size.width,\n    minHeight: component.size.height,\n    // Don't apply transform when dragging since we're using DragOverlay\n    transform: isDragging ? undefined : (transform ? `translate3d(${transform.x}px, ${transform.y}px, 0)` : undefined),\n    zIndex: isDragging ? 1000 : component.position.z || 1,\n    opacity: isDragging ? 0.5 : 1, // Make original component semi-transparent when dragging\n  }\n\n  const handleClick = (e: React.MouseEvent) => {\n    e.stopPropagation()\n    onSelect()\n  }\n\n  const handleDelete = (e: React.MouseEvent) => {\n    e.stopPropagation()\n    removeComponent(component.id)\n  }\n\n  const renderComponent = () => {\n    switch (component.type) {\n      case 'button':\n        return (\n          <Button \n            variant={component.props.variant || 'default'}\n            size={component.props.size || 'default'}\n            className=\"w-full\"\n          >\n            {component.props.text || 'Button'}\n          </Button>\n        )\n\n      case 'input':\n        return (\n          <Input\n            placeholder={component.props.placeholder || 'Enter text...'}\n            type={component.props.type || 'text'}\n            className=\"w-full\"\n          />\n        )\n\n      case 'textarea':\n        return (\n          <Textarea\n            placeholder={component.props.placeholder || 'Enter text...'}\n            className=\"w-full\"\n            rows={component.props.rows || 3}\n          />\n        )\n\n      case 'text':\n        return (\n          <p \n            className={`text-${component.props.fontSize || 'base'} ${component.props.className || ''}`}\n            style={{ color: component.props.color }}\n          >\n            {component.props.content || 'Text content'}\n          </p>\n        )\n\n      case 'heading':\n        const HeadingTag = `h${component.props.level || 1}` as keyof JSX.IntrinsicElements\n        return (\n          <HeadingTag \n            className={`font-bold ${component.props.className || ''}`}\n            style={{ color: component.props.color }}\n          >\n            {component.props.content || 'Heading'}\n          </HeadingTag>\n        )\n\n      case 'card':\n        return (\n          <Card className=\"w-full\">\n            <CardHeader>\n              <CardTitle>{component.props.title || 'Card Title'}</CardTitle>\n            </CardHeader>\n            <CardContent>\n              <p>{component.props.content || 'Card content goes here...'}</p>\n            </CardContent>\n          </Card>\n        )\n\n      case 'checkbox':\n        return (\n          <div className=\"flex items-center space-x-2\">\n            <Checkbox id={component.id} />\n            <Label htmlFor={component.id}>\n              {component.props.label || 'Checkbox label'}\n            </Label>\n          </div>\n        )\n\n      case 'container':\n        return (\n          <div \n            className={`border-2 border-dashed border-gray-300 rounded p-4 ${component.props.className || ''}`}\n            style={{ \n              backgroundColor: component.props.backgroundColor,\n              minHeight: '100px'\n            }}\n          >\n            <div className=\"text-center text-gray-500 text-sm\">\n              Container\n            </div>\n          </div>\n        )\n\n      default:\n        return (\n          <div className=\"border border-gray-300 rounded p-2 bg-gray-100\">\n            <Badge variant=\"secondary\">{component.type}</Badge>\n          </div>\n        )\n    }\n  }\n\n  return (\n    <div\n      ref={setNodeRef}\n      style={style}\n      onClick={handleClick}\n      className={`\n        group cursor-pointer\n        ${isSelected ? 'ring-2 ring-blue-500 ring-offset-2' : ''}\n        ${isDragging ? 'opacity-50' : ''}\n      `}\n    >\n      {/* Component Content */}\n      <div className=\"relative\">\n        {renderComponent()}\n        \n        {/* Selection Controls */}\n        {isSelected && (\n          <div className=\"absolute -top-8 left-0 flex items-center gap-1 bg-blue-500 text-white px-2 py-1 rounded text-xs\">\n            <span>{component.type}</span>\n            <button\n              onClick={handleDelete}\n              className=\"ml-2 hover:bg-blue-600 rounded px-1\"\n            >\n              ×\n            </button>\n          </div>\n        )}\n\n        {/* Drag Handle */}\n        <div\n          {...listeners}\n          {...attributes}\n          className=\"absolute inset-0 cursor-move opacity-0 group-hover:opacity-100 transition-opacity\"\n        />\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAXA;;;;;;;;;;AAmBO,SAAS,gBAAgB,KAAyD;QAAzD,EAAE,SAAS,EAAE,UAAU,EAAE,QAAQ,EAAwB,GAAzD;;IAC9B,MAAM,EAAE,eAAe,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD;IAE7D,MAAM,EAAE,UAAU,EAAE,SAAS,EAAE,UAAU,EAAE,SAAS,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,8JAAA,CAAA,eAAY,AAAD,EAAE;QAChF,IAAI,UAAU,EAAE;QAChB,MAAM;YACJ;YACA,YAAY;QACd;IACF;IAEA,MAAM,QAAQ;QACZ,UAAU;QACV,MAAM,UAAU,QAAQ,CAAC,CAAC;QAC1B,KAAK,UAAU,QAAQ,CAAC,CAAC;QACzB,OAAO,UAAU,IAAI,CAAC,KAAK;QAC3B,WAAW,UAAU,IAAI,CAAC,MAAM;QAChC,oEAAoE;QACpE,WAAW,aAAa,YAAa,YAAY,AAAC,eAAgC,OAAlB,UAAU,CAAC,EAAC,QAAkB,OAAZ,UAAU,CAAC,EAAC,YAAU;QACxG,QAAQ,aAAa,OAAO,UAAU,QAAQ,CAAC,CAAC,IAAI;QACpD,SAAS,aAAa,MAAM;IAC9B;IAEA,MAAM,cAAc,CAAC;QACnB,EAAE,eAAe;QACjB;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,EAAE,eAAe;QACjB,gBAAgB,UAAU,EAAE;IAC9B;IAEA,MAAM,kBAAkB;QACtB,OAAQ,UAAU,IAAI;YACpB,KAAK;gBACH,qBACE,6LAAC,qIAAA,CAAA,SAAM;oBACL,SAAS,UAAU,KAAK,CAAC,OAAO,IAAI;oBACpC,MAAM,UAAU,KAAK,CAAC,IAAI,IAAI;oBAC9B,WAAU;8BAET,UAAU,KAAK,CAAC,IAAI,IAAI;;;;;;YAI/B,KAAK;gBACH,qBACE,6LAAC,oIAAA,CAAA,QAAK;oBACJ,aAAa,UAAU,KAAK,CAAC,WAAW,IAAI;oBAC5C,MAAM,UAAU,KAAK,CAAC,IAAI,IAAI;oBAC9B,WAAU;;;;;;YAIhB,KAAK;gBACH,qBACE,6LAAC,uIAAA,CAAA,WAAQ;oBACP,aAAa,UAAU,KAAK,CAAC,WAAW,IAAI;oBAC5C,WAAU;oBACV,MAAM,UAAU,KAAK,CAAC,IAAI,IAAI;;;;;;YAIpC,KAAK;gBACH,qBACE,6LAAC;oBACC,WAAW,AAAC,QAA6C,OAAtC,UAAU,KAAK,CAAC,QAAQ,IAAI,QAAO,KAAmC,OAAhC,UAAU,KAAK,CAAC,SAAS,IAAI;oBACtF,OAAO;wBAAE,OAAO,UAAU,KAAK,CAAC,KAAK;oBAAC;8BAErC,UAAU,KAAK,CAAC,OAAO,IAAI;;;;;;YAIlC,KAAK;gBACH,MAAM,aAAa,AAAC,IAA8B,OAA3B,UAAU,KAAK,CAAC,KAAK,IAAI;gBAChD,qBACE,6LAAC;oBACC,WAAW,AAAC,aAA4C,OAAhC,UAAU,KAAK,CAAC,SAAS,IAAI;oBACrD,OAAO;wBAAE,OAAO,UAAU,KAAK,CAAC,KAAK;oBAAC;8BAErC,UAAU,KAAK,CAAC,OAAO,IAAI;;;;;;YAIlC,KAAK;gBACH,qBACE,6LAAC,mIAAA,CAAA,OAAI;oBAAC,WAAU;;sCACd,6LAAC,mIAAA,CAAA,aAAU;sCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;0CAAE,UAAU,KAAK,CAAC,KAAK,IAAI;;;;;;;;;;;sCAEvC,6LAAC,mIAAA,CAAA,cAAW;sCACV,cAAA,6LAAC;0CAAG,UAAU,KAAK,CAAC,OAAO,IAAI;;;;;;;;;;;;;;;;;YAKvC,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,uIAAA,CAAA,WAAQ;4BAAC,IAAI,UAAU,EAAE;;;;;;sCAC1B,6LAAC,oIAAA,CAAA,QAAK;4BAAC,SAAS,UAAU,EAAE;sCACzB,UAAU,KAAK,CAAC,KAAK,IAAI;;;;;;;;;;;;YAKlC,KAAK;gBACH,qBACE,6LAAC;oBACC,WAAW,AAAC,sDAAqF,OAAhC,UAAU,KAAK,CAAC,SAAS,IAAI;oBAC9F,OAAO;wBACL,iBAAiB,UAAU,KAAK,CAAC,eAAe;wBAChD,WAAW;oBACb;8BAEA,cAAA,6LAAC;wBAAI,WAAU;kCAAoC;;;;;;;;;;;YAMzD;gBACE,qBACE,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;wBAAC,SAAQ;kCAAa,UAAU,IAAI;;;;;;;;;;;QAGlD;IACF;IAEA,qBACE,6LAAC;QACC,KAAK;QACL,OAAO;QACP,SAAS;QACT,WAAW,AAAC,2CAGR,OADA,aAAa,uCAAuC,IAAG,cACxB,OAA/B,aAAa,eAAe,IAAG;kBAInC,cAAA,6LAAC;YAAI,WAAU;;gBACZ;gBAGA,4BACC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;sCAAM,UAAU,IAAI;;;;;;sCACrB,6LAAC;4BACC,SAAS;4BACT,WAAU;sCACX;;;;;;;;;;;;8BAOL,6LAAC;oBACE,GAAG,SAAS;oBACZ,GAAG,UAAU;oBACd,WAAU;;;;;;;;;;;;;;;;;AAKpB;GAxKgB;;QAC+B,sHAAA,CAAA,oBAAiB;QAEO,8JAAA,CAAA,eAAY;;;KAHnE", "debugId": null}}, {"offset": {"line": 1783, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/FlowUi/src/components/ui/loading-spinner.tsx"], "sourcesContent": ["import { cn } from '@/lib/utils'\n\ninterface LoadingSpinnerProps {\n  size?: 'sm' | 'md' | 'lg'\n  className?: string\n}\n\nexport function LoadingSpinner({ size = 'md', className }: LoadingSpinnerProps) {\n  const sizeClasses = {\n    sm: 'w-4 h-4',\n    md: 'w-6 h-6',\n    lg: 'w-8 h-8'\n  }\n\n  return (\n    <div\n      className={cn(\n        'animate-spin rounded-full border-2 border-gray-300 border-t-blue-600',\n        sizeClasses[size],\n        className\n      )}\n    />\n  )\n}\n\nexport function LoadingOverlay({ children }: { children?: React.ReactNode }) {\n  return (\n    <div className=\"absolute inset-0 bg-white/80 dark:bg-gray-900/80 flex items-center justify-center z-50\">\n      <div className=\"flex flex-col items-center gap-2\">\n        <LoadingSpinner size=\"lg\" />\n        {children && (\n          <p className=\"text-sm text-gray-600 dark:text-gray-400\">{children}</p>\n        )}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAAA;;;AAOO,SAAS,eAAe,KAA+C;QAA/C,EAAE,OAAO,IAAI,EAAE,SAAS,EAAuB,GAA/C;IAC7B,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wEACA,WAAW,CAAC,KAAK,EACjB;;;;;;AAIR;KAhBgB;AAkBT,SAAS,eAAe,KAA4C;QAA5C,EAAE,QAAQ,EAAkC,GAA5C;IAC7B,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAe,MAAK;;;;;;gBACpB,0BACC,6LAAC;oBAAE,WAAU;8BAA4C;;;;;;;;;;;;;;;;;AAKnE;MAXgB", "debugId": null}}, {"offset": {"line": 1854, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/FlowUi/src/components/builder/builder-canvas.tsx"], "sourcesContent": ["'use client'\n\nimport { useDroppable } from '@dnd-kit/core'\nimport { Card } from '@/components/ui/card'\nimport { useUIBuilderStore } from '@/lib/store'\nimport { CanvasComponent } from './canvas-component'\nimport { LoadingOverlay } from '@/components/ui/loading-spinner'\n\nexport function BuilderCanvas() {\n  const { currentProject, selectedComponent, setSelectedComponent, isLoading, isSaving } = useUIBuilderStore()\n  \n  const { setNodeRef, isOver } = useDroppable({\n    id: 'canvas',\n  })\n\n  const components = currentProject?.components || []\n\n  const handleCanvasClick = (e: React.MouseEvent) => {\n    // Only deselect if clicking on the canvas itself, not on a component\n    if (e.target === e.currentTarget) {\n      setSelectedComponent(null)\n    }\n  }\n\n  return (\n    <div className=\"h-full p-4\">\n      <div className=\"h-full max-w-4xl mx-auto\">\n        <div\n          ref={setNodeRef}\n          onClick={handleCanvasClick}\n          data-canvas=\"true\"\n          className={`\n            relative h-full min-h-[600px] bg-white dark:bg-gray-800 rounded-lg border-2 border-dashed\n            ${isOver\n              ? 'border-blue-400 bg-blue-50 dark:bg-blue-900/20'\n              : 'border-gray-300 dark:border-gray-600'\n            }\n            transition-colors duration-200\n          `}\n        >\n          {/* Canvas Grid Background */}\n          <div \n            className=\"absolute inset-0 opacity-30\"\n            style={{\n              backgroundImage: `\n                linear-gradient(rgba(0,0,0,0.1) 1px, transparent 1px),\n                linear-gradient(90deg, rgba(0,0,0,0.1) 1px, transparent 1px)\n              `,\n              backgroundSize: '20px 20px'\n            }}\n          />\n\n          {/* Empty State */}\n          {components.length === 0 && (\n            <div className=\"absolute inset-0 flex items-center justify-center\">\n              <div className=\"text-center\">\n                <div className=\"w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4\">\n                  <svg className=\"w-8 h-8 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\" />\n                  </svg>\n                </div>\n                <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2\">\n                  Start Building\n                </h3>\n                <p className=\"text-gray-500 dark:text-gray-400 max-w-sm\">\n                  Drag components from the palette on the left to start building your interface\n                </p>\n              </div>\n            </div>\n          )}\n\n          {/* Render Components */}\n          {components.map((component) => (\n            <CanvasComponent\n              key={component.id}\n              component={component}\n              isSelected={selectedComponent === component.id}\n              onSelect={() => setSelectedComponent(component.id)}\n            />\n          ))}\n\n          {/* Selection Indicator */}\n          {isOver && (\n            <div className=\"absolute inset-0 border-2 border-blue-400 rounded-lg pointer-events-none\" />\n          )}\n\n          {/* Loading overlay */}\n          {isLoading && (\n            <LoadingOverlay>\n              Loading project...\n            </LoadingOverlay>\n          )}\n        </div>\n\n        {/* Saving indicator */}\n        {isSaving && (\n          <div className=\"absolute top-4 right-4 bg-white dark:bg-gray-800 rounded-lg shadow-lg p-3 flex items-center gap-2 z-40\">\n            <div className=\"w-4 h-4 animate-spin rounded-full border-2 border-gray-300 border-t-blue-600\"></div>\n            <span className=\"text-sm text-gray-600 dark:text-gray-400\">Saving...</span>\n          </div>\n        )}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AACA;;;AANA;;;;;AAQO,SAAS;;IACd,MAAM,EAAE,cAAc,EAAE,iBAAiB,EAAE,oBAAoB,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD;IAEzG,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,8JAAA,CAAA,eAAY,AAAD,EAAE;QAC1C,IAAI;IACN;IAEA,MAAM,aAAa,CAAA,2BAAA,qCAAA,eAAgB,UAAU,KAAI,EAAE;IAEnD,MAAM,oBAAoB,CAAC;QACzB,qEAAqE;QACrE,IAAI,EAAE,MAAM,KAAK,EAAE,aAAa,EAAE;YAChC,qBAAqB;QACvB;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBACC,KAAK;oBACL,SAAS;oBACT,eAAY;oBACZ,WAAW,AAAC,wHAKT,OAHC,SACE,mDACA,wCACH;;sCAKH,6LAAC;4BACC,WAAU;4BACV,OAAO;gCACL,iBAAkB;gCAIlB,gBAAgB;4BAClB;;;;;;wBAID,WAAW,MAAM,KAAK,mBACrB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;4CAAwB,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDAC/E,cAAA,6LAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;;;;;;kDAGzE,6LAAC;wCAAG,WAAU;kDAA4D;;;;;;kDAG1E,6LAAC;wCAAE,WAAU;kDAA4C;;;;;;;;;;;;;;;;;wBAQ9D,WAAW,GAAG,CAAC,CAAC,0BACf,6LAAC,uJAAA,CAAA,kBAAe;gCAEd,WAAW;gCACX,YAAY,sBAAsB,UAAU,EAAE;gCAC9C,UAAU,IAAM,qBAAqB,UAAU,EAAE;+BAH5C,UAAU,EAAE;;;;;wBAQpB,wBACC,6LAAC;4BAAI,WAAU;;;;;;wBAIhB,2BACC,6LAAC,iJAAA,CAAA,iBAAc;sCAAC;;;;;;;;;;;;gBAOnB,0BACC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAK,WAAU;sCAA2C;;;;;;;;;;;;;;;;;;;;;;;AAMvE;GAhGgB;;QAC2E,sHAAA,CAAA,oBAAiB;QAE3E,8JAAA,CAAA,eAAY;;;KAH7B", "debugId": null}}, {"offset": {"line": 2046, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/FlowUi/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,KAEoC;QAFpC,EACd,GAAG,OAC+C,GAFpC;IAGd,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,YAAY,KAEgC;QAFhC,EACnB,GAAG,OACgD,GAFhC;IAGnB,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,YAAY,KAEgC;QAFhC,EACnB,GAAG,OACgD,GAFhC;IAGnB,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,KAOtB;QAPsB,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ,GAPsB;IAQrB,qBACE,6LAAC,qKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,qKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,6LAAC,2NAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;MAxBS;AA0BT,SAAS,cAAc,KAKgC;QALhC,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD,GALhC;IAMrB,qBACE,6LAAC,qKAAA,CAAA,SAAsB;kBACrB,cAAA,6LAAC,qKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6LAAC;;;;;8BACD,6LAAC,qKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6LAAC;;;;;;;;;;;;;;;;AAIT;MAjCS;AAmCT,SAAS,YAAY,KAGgC;QAHhC,EACnB,SAAS,EACT,GAAG,OACgD,GAHhC;IAInB,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,WAAW,KAIgC;QAJhC,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C,GAJhC;IAKlB,qBACE,6LAAC,qKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,qKAAA,CAAA,gBAA6B;8BAC5B,cAAA,6LAAC,2MAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,6LAAC,qKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;MAtBS;AAwBT,SAAS,gBAAgB,KAGgC;QAHhC,EACvB,SAAS,EACT,GAAG,OACoD,GAHhC;IAIvB,qBACE,6LAAC,qKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,qBAAqB,KAGgC;QAHhC,EAC5B,SAAS,EACT,GAAG,OACyD,GAHhC;IAI5B,qBACE,6LAAC,qKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;MAhBS;AAkBT,SAAS,uBAAuB,KAGgC;QAHhC,EAC9B,SAAS,EACT,GAAG,OAC2D,GAHhC;IAI9B,qBACE,6LAAC,qKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,2NAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC;MAhBS", "debugId": null}}, {"offset": {"line": 2305, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/FlowUi/src/components/builder/property-panel.tsx"], "sourcesContent": ["'use client'\n\nimport { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'\nimport { Textarea } from '@/components/ui/textarea'\nimport { useUIBuilderStore } from '@/lib/store'\nimport { Badge } from '@/components/ui/badge'\n\nexport function PropertyPanel() {\n  const { currentProject, selectedComponent, updateComponent } = useUIBuilderStore()\n\n  const selectedComponentData = currentProject?.components.find(\n    (comp) => comp.id === selectedComponent\n  )\n\n  const handlePropertyChange = (property: string, value: any) => {\n    if (!selectedComponent) return\n    \n    updateComponent(selectedComponent, {\n      props: {\n        ...selectedComponentData?.props,\n        [property]: value,\n      },\n    })\n  }\n\n  const handlePositionChange = (axis: 'x' | 'y', value: number) => {\n    if (!selectedComponent || !selectedComponentData) return\n    \n    updateComponent(selectedComponent, {\n      position: {\n        ...selectedComponentData.position,\n        [axis]: value,\n      },\n    })\n  }\n\n  const handleSizeChange = (dimension: 'width' | 'height', value: number) => {\n    if (!selectedComponent || !selectedComponentData) return\n    \n    updateComponent(selectedComponent, {\n      size: {\n        ...selectedComponentData.size,\n        [dimension]: value,\n      },\n    })\n  }\n\n  if (!selectedComponent || !selectedComponentData) {\n    return (\n      <div className=\"p-4\">\n        <h2 className=\"text-lg font-semibold mb-4\">Properties</h2>\n        <div className=\"text-center py-8\">\n          <div className=\"w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4\">\n            <svg className=\"w-8 h-8 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 15l-2 5L9 9l11 4-5 2zm0 0l5 5M7.188 2.239l.777 2.897M5.136 7.965l-2.898-.777M13.95 4.05l-2.122 2.122m-5.657 5.656l-2.121 2.122\" />\n            </svg>\n          </div>\n          <h3 className=\"text-lg font-medium mb-2\">No Component Selected</h3>\n          <p className=\"text-muted-foreground text-sm\">\n            Select a component on the canvas to edit its properties\n          </p>\n        </div>\n      </div>\n    )\n  }\n\n  const renderComponentProperties = () => {\n    switch (selectedComponentData.type) {\n      case 'button':\n        return (\n          <>\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"button-text\">Text</Label>\n              <Input\n                id=\"button-text\"\n                value={selectedComponentData.props.text || ''}\n                onChange={(e) => handlePropertyChange('text', e.target.value)}\n                placeholder=\"Button text\"\n              />\n            </div>\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"button-variant\">Variant</Label>\n              <Select\n                value={selectedComponentData.props.variant || 'default'}\n                onValueChange={(value) => handlePropertyChange('variant', value)}\n              >\n                <SelectTrigger>\n                  <SelectValue />\n                </SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"default\">Default</SelectItem>\n                  <SelectItem value=\"destructive\">Destructive</SelectItem>\n                  <SelectItem value=\"outline\">Outline</SelectItem>\n                  <SelectItem value=\"secondary\">Secondary</SelectItem>\n                  <SelectItem value=\"ghost\">Ghost</SelectItem>\n                  <SelectItem value=\"link\">Link</SelectItem>\n                </SelectContent>\n              </Select>\n            </div>\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"button-size\">Size</Label>\n              <Select\n                value={selectedComponentData.props.size || 'default'}\n                onValueChange={(value) => handlePropertyChange('size', value)}\n              >\n                <SelectTrigger>\n                  <SelectValue />\n                </SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"sm\">Small</SelectItem>\n                  <SelectItem value=\"default\">Default</SelectItem>\n                  <SelectItem value=\"lg\">Large</SelectItem>\n                </SelectContent>\n              </Select>\n            </div>\n          </>\n        )\n\n      case 'input':\n        return (\n          <>\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"input-placeholder\">Placeholder</Label>\n              <Input\n                id=\"input-placeholder\"\n                value={selectedComponentData.props.placeholder || ''}\n                onChange={(e) => handlePropertyChange('placeholder', e.target.value)}\n                placeholder=\"Enter placeholder text\"\n              />\n            </div>\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"input-type\">Type</Label>\n              <Select\n                value={selectedComponentData.props.type || 'text'}\n                onValueChange={(value) => handlePropertyChange('type', value)}\n              >\n                <SelectTrigger>\n                  <SelectValue />\n                </SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"text\">Text</SelectItem>\n                  <SelectItem value=\"email\">Email</SelectItem>\n                  <SelectItem value=\"password\">Password</SelectItem>\n                  <SelectItem value=\"number\">Number</SelectItem>\n                  <SelectItem value=\"tel\">Phone</SelectItem>\n                  <SelectItem value=\"url\">URL</SelectItem>\n                </SelectContent>\n              </Select>\n            </div>\n          </>\n        )\n\n      case 'text':\n        return (\n          <>\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"text-content\">Content</Label>\n              <Textarea\n                id=\"text-content\"\n                value={selectedComponentData.props.content || ''}\n                onChange={(e) => handlePropertyChange('content', e.target.value)}\n                placeholder=\"Enter text content\"\n                rows={3}\n              />\n            </div>\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"text-color\">Color</Label>\n              <Input\n                id=\"text-color\"\n                type=\"color\"\n                value={selectedComponentData.props.color || '#000000'}\n                onChange={(e) => handlePropertyChange('color', e.target.value)}\n              />\n            </div>\n          </>\n        )\n\n      case 'heading':\n        return (\n          <>\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"heading-content\">Content</Label>\n              <Input\n                id=\"heading-content\"\n                value={selectedComponentData.props.content || ''}\n                onChange={(e) => handlePropertyChange('content', e.target.value)}\n                placeholder=\"Heading text\"\n              />\n            </div>\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"heading-level\">Level</Label>\n              <Select\n                value={selectedComponentData.props.level?.toString() || '1'}\n                onValueChange={(value) => handlePropertyChange('level', parseInt(value))}\n              >\n                <SelectTrigger>\n                  <SelectValue />\n                </SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"1\">H1</SelectItem>\n                  <SelectItem value=\"2\">H2</SelectItem>\n                  <SelectItem value=\"3\">H3</SelectItem>\n                  <SelectItem value=\"4\">H4</SelectItem>\n                  <SelectItem value=\"5\">H5</SelectItem>\n                  <SelectItem value=\"6\">H6</SelectItem>\n                </SelectContent>\n              </Select>\n            </div>\n          </>\n        )\n\n      default:\n        return (\n          <div className=\"text-center py-4\">\n            <p className=\"text-muted-foreground text-sm\">\n              Properties for {selectedComponentData.type} component coming soon\n            </p>\n          </div>\n        )\n    }\n  }\n\n  return (\n    <div className=\"p-4 space-y-6\">\n      <div>\n        <h2 className=\"text-lg font-semibold mb-2\">Properties</h2>\n        <Badge variant=\"secondary\">{selectedComponentData.type}</Badge>\n      </div>\n\n      {/* Component Properties */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"text-sm\">Component</CardTitle>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          {renderComponentProperties()}\n        </CardContent>\n      </Card>\n\n      {/* Position & Size */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"text-sm\">Position & Size</CardTitle>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          <div className=\"grid grid-cols-2 gap-2\">\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"pos-x\">X</Label>\n              <Input\n                id=\"pos-x\"\n                type=\"number\"\n                value={selectedComponentData.position.x}\n                onChange={(e) => handlePositionChange('x', parseInt(e.target.value) || 0)}\n              />\n            </div>\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"pos-y\">Y</Label>\n              <Input\n                id=\"pos-y\"\n                type=\"number\"\n                value={selectedComponentData.position.y}\n                onChange={(e) => handlePositionChange('y', parseInt(e.target.value) || 0)}\n              />\n            </div>\n          </div>\n          <div className=\"grid grid-cols-2 gap-2\">\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"size-width\">Width</Label>\n              <Input\n                id=\"size-width\"\n                type=\"number\"\n                value={selectedComponentData.size.width}\n                onChange={(e) => handleSizeChange('width', parseInt(e.target.value) || 0)}\n              />\n            </div>\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"size-height\">Height</Label>\n              <Input\n                id=\"size-height\"\n                type=\"number\"\n                value={selectedComponentData.size.height}\n                onChange={(e) => handleSizeChange('height', parseInt(e.target.value) || 0)}\n              />\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;AARA;;;;;;;;AAUO,SAAS;;IACd,MAAM,EAAE,cAAc,EAAE,iBAAiB,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD;IAE/E,MAAM,wBAAwB,2BAAA,qCAAA,eAAgB,UAAU,CAAC,IAAI,CAC3D,CAAC,OAAS,KAAK,EAAE,KAAK;IAGxB,MAAM,uBAAuB,CAAC,UAAkB;QAC9C,IAAI,CAAC,mBAAmB;QAExB,gBAAgB,mBAAmB;YACjC,OAAO;mBACF,kCAAA,4CAAA,sBAAuB,KAAK,AAA/B;gBACA,CAAC,SAAS,EAAE;YACd;QACF;IACF;IAEA,MAAM,uBAAuB,CAAC,MAAiB;QAC7C,IAAI,CAAC,qBAAqB,CAAC,uBAAuB;QAElD,gBAAgB,mBAAmB;YACjC,UAAU;gBACR,GAAG,sBAAsB,QAAQ;gBACjC,CAAC,KAAK,EAAE;YACV;QACF;IACF;IAEA,MAAM,mBAAmB,CAAC,WAA+B;QACvD,IAAI,CAAC,qBAAqB,CAAC,uBAAuB;QAElD,gBAAgB,mBAAmB;YACjC,MAAM;gBACJ,GAAG,sBAAsB,IAAI;gBAC7B,CAAC,UAAU,EAAE;YACf;QACF;IACF;IAEA,IAAI,CAAC,qBAAqB,CAAC,uBAAuB;QAChD,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAG,WAAU;8BAA6B;;;;;;8BAC3C,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;gCAAwB,MAAK;gCAAO,QAAO;gCAAe,SAAQ;0CAC/E,cAAA,6LAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;;;;;;sCAGzE,6LAAC;4BAAG,WAAU;sCAA2B;;;;;;sCACzC,6LAAC;4BAAE,WAAU;sCAAgC;;;;;;;;;;;;;;;;;;IAMrD;IAEA,MAAM,4BAA4B;QAChC,OAAQ,sBAAsB,IAAI;YAChC,KAAK;gBACH,qBACE;;sCACE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAc;;;;;;8CAC7B,6LAAC,oIAAA,CAAA,QAAK;oCACJ,IAAG;oCACH,OAAO,sBAAsB,KAAK,CAAC,IAAI,IAAI;oCAC3C,UAAU,CAAC,IAAM,qBAAqB,QAAQ,EAAE,MAAM,CAAC,KAAK;oCAC5D,aAAY;;;;;;;;;;;;sCAGhB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAiB;;;;;;8CAChC,6LAAC,qIAAA,CAAA,SAAM;oCACL,OAAO,sBAAsB,KAAK,CAAC,OAAO,IAAI;oCAC9C,eAAe,CAAC,QAAU,qBAAqB,WAAW;;sDAE1D,6LAAC,qIAAA,CAAA,gBAAa;sDACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;;;;;;;;;;sDAEd,6LAAC,qIAAA,CAAA,gBAAa;;8DACZ,6LAAC,qIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAU;;;;;;8DAC5B,6LAAC,qIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAc;;;;;;8DAChC,6LAAC,qIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAU;;;;;;8DAC5B,6LAAC,qIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAY;;;;;;8DAC9B,6LAAC,qIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAQ;;;;;;8DAC1B,6LAAC,qIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAO;;;;;;;;;;;;;;;;;;;;;;;;sCAI/B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAc;;;;;;8CAC7B,6LAAC,qIAAA,CAAA,SAAM;oCACL,OAAO,sBAAsB,KAAK,CAAC,IAAI,IAAI;oCAC3C,eAAe,CAAC,QAAU,qBAAqB,QAAQ;;sDAEvD,6LAAC,qIAAA,CAAA,gBAAa;sDACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;;;;;;;;;;sDAEd,6LAAC,qIAAA,CAAA,gBAAa;;8DACZ,6LAAC,qIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAK;;;;;;8DACvB,6LAAC,qIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAU;;;;;;8DAC5B,6LAAC,qIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;YAOnC,KAAK;gBACH,qBACE;;sCACE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAoB;;;;;;8CACnC,6LAAC,oIAAA,CAAA,QAAK;oCACJ,IAAG;oCACH,OAAO,sBAAsB,KAAK,CAAC,WAAW,IAAI;oCAClD,UAAU,CAAC,IAAM,qBAAqB,eAAe,EAAE,MAAM,CAAC,KAAK;oCACnE,aAAY;;;;;;;;;;;;sCAGhB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAa;;;;;;8CAC5B,6LAAC,qIAAA,CAAA,SAAM;oCACL,OAAO,sBAAsB,KAAK,CAAC,IAAI,IAAI;oCAC3C,eAAe,CAAC,QAAU,qBAAqB,QAAQ;;sDAEvD,6LAAC,qIAAA,CAAA,gBAAa;sDACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;;;;;;;;;;sDAEd,6LAAC,qIAAA,CAAA,gBAAa;;8DACZ,6LAAC,qIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAO;;;;;;8DACzB,6LAAC,qIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAQ;;;;;;8DAC1B,6LAAC,qIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAW;;;;;;8DAC7B,6LAAC,qIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAS;;;;;;8DAC3B,6LAAC,qIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAM;;;;;;8DACxB,6LAAC,qIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAM;;;;;;;;;;;;;;;;;;;;;;;;;;YAOpC,KAAK;gBACH,qBACE;;sCACE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAe;;;;;;8CAC9B,6LAAC,uIAAA,CAAA,WAAQ;oCACP,IAAG;oCACH,OAAO,sBAAsB,KAAK,CAAC,OAAO,IAAI;oCAC9C,UAAU,CAAC,IAAM,qBAAqB,WAAW,EAAE,MAAM,CAAC,KAAK;oCAC/D,aAAY;oCACZ,MAAM;;;;;;;;;;;;sCAGV,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAa;;;;;;8CAC5B,6LAAC,oIAAA,CAAA,QAAK;oCACJ,IAAG;oCACH,MAAK;oCACL,OAAO,sBAAsB,KAAK,CAAC,KAAK,IAAI;oCAC5C,UAAU,CAAC,IAAM,qBAAqB,SAAS,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;;;YAMvE,KAAK;oBAeY;gBAdf,qBACE;;sCACE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAkB;;;;;;8CACjC,6LAAC,oIAAA,CAAA,QAAK;oCACJ,IAAG;oCACH,OAAO,sBAAsB,KAAK,CAAC,OAAO,IAAI;oCAC9C,UAAU,CAAC,IAAM,qBAAqB,WAAW,EAAE,MAAM,CAAC,KAAK;oCAC/D,aAAY;;;;;;;;;;;;sCAGhB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAgB;;;;;;8CAC/B,6LAAC,qIAAA,CAAA,SAAM;oCACL,OAAO,EAAA,qCAAA,sBAAsB,KAAK,CAAC,KAAK,cAAjC,yDAAA,mCAAmC,QAAQ,OAAM;oCACxD,eAAe,CAAC,QAAU,qBAAqB,SAAS,SAAS;;sDAEjE,6LAAC,qIAAA,CAAA,gBAAa;sDACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;;;;;;;;;;sDAEd,6LAAC,qIAAA,CAAA,gBAAa;;8DACZ,6LAAC,qIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAI;;;;;;8DACtB,6LAAC,qIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAI;;;;;;8DACtB,6LAAC,qIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAI;;;;;;8DACtB,6LAAC,qIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAI;;;;;;8DACtB,6LAAC,qIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAI;;;;;;8DACtB,6LAAC,qIAAA,CAAA,aAAU;oDAAC,OAAM;8DAAI;;;;;;;;;;;;;;;;;;;;;;;;;;YAOlC;gBACE,qBACE,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAE,WAAU;;4BAAgC;4BAC3B,sBAAsB,IAAI;4BAAC;;;;;;;;;;;;QAIrD;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;;kCACC,6LAAC;wBAAG,WAAU;kCAA6B;;;;;;kCAC3C,6LAAC,oIAAA,CAAA,QAAK;wBAAC,SAAQ;kCAAa,sBAAsB,IAAI;;;;;;;;;;;;0BAIxD,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4BAAC,WAAU;sCAAU;;;;;;;;;;;kCAEjC,6LAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;kCACpB;;;;;;;;;;;;0BAKL,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4BAAC,WAAU;sCAAU;;;;;;;;;;;kCAEjC,6LAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,oIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAQ;;;;;;0DACvB,6LAAC,oIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,MAAK;gDACL,OAAO,sBAAsB,QAAQ,CAAC,CAAC;gDACvC,UAAU,CAAC,IAAM,qBAAqB,KAAK,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;;;;;;;;;;;;kDAG3E,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,oIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAQ;;;;;;0DACvB,6LAAC,oIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,MAAK;gDACL,OAAO,sBAAsB,QAAQ,CAAC,CAAC;gDACvC,UAAU,CAAC,IAAM,qBAAqB,KAAK,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;;;;;;;;;;;;;;;;;;0CAI7E,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,oIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAa;;;;;;0DAC5B,6LAAC,oIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,MAAK;gDACL,OAAO,sBAAsB,IAAI,CAAC,KAAK;gDACvC,UAAU,CAAC,IAAM,iBAAiB,SAAS,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;;;;;;;;;;;;kDAG3E,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,oIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAc;;;;;;0DAC7B,6LAAC,oIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,MAAK;gDACL,OAAO,sBAAsB,IAAI,CAAC,MAAM;gDACxC,UAAU,CAAC,IAAM,iBAAiB,UAAU,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQxF;GA1RgB;;QACiD,sHAAA,CAAA,oBAAiB;;;KADlE", "debugId": null}}, {"offset": {"line": 3199, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/FlowUi/src/components/builder/builder-header.tsx"], "sourcesContent": ["'use client'\n\nimport { But<PERSON> } from '@/components/ui/button'\nimport { Badge } from '@/components/ui/badge'\nimport { useUIBuilderStore } from '@/lib/store'\nimport { useRouter } from 'next/navigation'\nimport { useEffect } from 'react'\nimport {\n  Eye,\n  EyeOff,\n  Save,\n  Undo,\n  Redo,\n  Settings,\n  Share,\n  ArrowLeft,\n  Globe,\n  AlertCircle,\n  CheckCircle\n} from 'lucide-react'\n\nexport function BuilderHeader() {\n  const {\n    currentProject,\n    isPreviewMode,\n    isSaving,\n    error,\n    togglePreviewMode,\n    setCurrentProject,\n    saveProject,\n    createProject,\n    setError,\n    undo,\n    redo,\n    canUndo,\n    canRedo\n  } = useUIBuilderStore()\n  const router = useRouter()\n\n  const handleSave = async () => {\n    if (!currentProject) return\n\n    try {\n      // Ensure user and organization exist before saving\n      const ensureResponse = await fetch('/api/auth/ensure-user', {\n        method: 'POST'\n      })\n\n      if (!ensureResponse.ok) {\n        const ensureError = await ensureResponse.json()\n        throw new Error(`Setup failed: ${ensureError.error}`)\n      }\n\n      // If it's a temporary project, create a new one\n      if (currentProject.id === 'temp-project') {\n        console.log('Creating new project from temp project')\n        await createProject(currentProject.name, currentProject.description)\n      } else {\n        console.log('Saving existing project')\n        await saveProject()\n      }\n\n      // Success feedback is now handled by the store via toasts\n    } catch (err) {\n      console.error('Save failed:', err)\n      // Show error toast if not already handled by store\n      if (typeof window !== 'undefined' && (window as any).addToast) {\n        ;(window as any).addToast({\n          type: 'error',\n          title: 'Save failed',\n          description: err instanceof Error ? err.message : 'Unknown error occurred'\n        })\n      }\n    }\n  }\n\n  // Debug function to check user status\n  const handleDebugUser = async () => {\n    try {\n      const response = await fetch('/api/debug/user')\n      const data = await response.json()\n      console.log('User debug info:', data)\n      alert(`User Debug Info:\\n${JSON.stringify(data, null, 2)}`)\n    } catch (error) {\n      console.error('Debug failed:', error)\n      alert('Debug failed - check console')\n    }\n  }\n\n  // Setup account function\n  const handleSetupAccount = async () => {\n    try {\n      const response = await fetch('/api/auth/ensure-user', {\n        method: 'POST'\n      })\n\n      if (response.ok) {\n        if (typeof window !== 'undefined' && (window as any).addToast) {\n          ;(window as any).addToast({\n            type: 'success',\n            title: 'Account setup complete',\n            description: 'Your account has been properly initialized. You can now save projects.'\n          })\n        }\n      } else {\n        const errorData = await response.json()\n        throw new Error(errorData.error)\n      }\n    } catch (error) {\n      console.error('Setup failed:', error)\n      if (typeof window !== 'undefined' && (window as any).addToast) {\n        ;(window as any).addToast({\n          type: 'error',\n          title: 'Setup failed',\n          description: error instanceof Error ? error.message : 'Unknown error occurred'\n        })\n      }\n    }\n  }\n\n  // Clear error after 5 seconds\n  useEffect(() => {\n    if (error) {\n      const timer = setTimeout(() => {\n        setError(null)\n      }, 5000)\n      return () => clearTimeout(timer)\n    }\n  }, [error, setError])\n\n  // Ensure user and organization exist on component mount\n  useEffect(() => {\n    const ensureUser = async () => {\n      try {\n        const response = await fetch('/api/auth/ensure-user', {\n          method: 'POST'\n        })\n\n        if (!response.ok) {\n          const errorData = await response.json()\n          console.error('Failed to ensure user:', errorData)\n        } else {\n          console.log('User and organization ensured')\n        }\n      } catch (error) {\n        console.error('Error ensuring user:', error)\n      }\n    }\n\n    ensureUser()\n  }, [])\n\n  const handleShare = () => {\n    if (!currentProject || currentProject.components.length === 0) {\n      alert('Please add some components to your project before sharing.')\n      return\n    }\n\n    // Generate a shareable link\n    const projectData = {\n      name: currentProject.name,\n      components: currentProject.components,\n      timestamp: Date.now()\n    }\n\n    // Encode the project data\n    const encodedData = btoa(JSON.stringify(projectData))\n    const shareUrl = `${window.location.origin}/preview/${encodedData}`\n\n    // Copy to clipboard\n    navigator.clipboard.writeText(shareUrl).then(() => {\n      alert(`🎉 Share link copied to clipboard!\\n\\n${shareUrl}\\n\\nAnyone with this link can view your project.`)\n    }).catch(() => {\n      // Fallback for older browsers\n      const textArea = document.createElement('textarea')\n      textArea.value = shareUrl\n      document.body.appendChild(textArea)\n      textArea.select()\n      document.execCommand('copy')\n      document.body.removeChild(textArea)\n      alert(`🎉 Share link created!\\n\\n${shareUrl}\\n\\nLink has been copied to clipboard.`)\n    })\n  }\n\n  const handlePublish = () => {\n    if (!currentProject || currentProject.components.length === 0) {\n      alert('Please add some components to your project before publishing.')\n      return\n    }\n\n    // Generate a public URL\n    const projectData = {\n      name: currentProject.name,\n      components: currentProject.components,\n      timestamp: Date.now(),\n      published: true\n    }\n\n    // Encode the project data\n    const encodedData = btoa(JSON.stringify(projectData))\n    const publicUrl = `${window.location.origin}/p/${encodedData}`\n\n    // Copy to clipboard and show success\n    navigator.clipboard.writeText(publicUrl).then(() => {\n      alert(`🚀 Project published successfully!\\n\\nPublic URL: ${publicUrl}\\n\\nYour project is now live and accessible to anyone with this link. The URL has been copied to your clipboard.`)\n    }).catch(() => {\n      // Fallback for older browsers\n      const textArea = document.createElement('textarea')\n      textArea.value = publicUrl\n      document.body.appendChild(textArea)\n      textArea.select()\n      document.execCommand('copy')\n      document.body.removeChild(textArea)\n      alert(`🚀 Project published successfully!\\n\\nPublic URL: ${publicUrl}\\n\\nYour project is now live and accessible to anyone.`)\n    })\n  }\n\n  const handleSettings = () => {\n    // TODO: Implement settings functionality\n    alert('🚧 Settings functionality coming soon!\\n\\nThis will open project settings including:\\n• Theme configuration\\n• Webhook settings\\n• Publishing options')\n  }\n\n  const handleUndo = () => {\n    undo()\n  }\n\n  const handleRedo = () => {\n    redo()\n  }\n\n  const handleBackToDashboard = () => {\n    router.push('/dashboard')\n  }\n\n  // Create a default project if none exists\n  if (!currentProject) {\n    setCurrentProject({\n      id: 'temp-project',\n      name: 'Untitled Project',\n      description: 'A new FlowUI project',\n      is_published: false,\n      components: [],\n    })\n  }\n\n  return (\n    <header className=\"bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700\">\n      {/* Error Banner */}\n      {error && (\n        <div className=\"bg-red-50 dark:bg-red-900/20 border-b border-red-200 dark:border-red-800 px-4 py-2\">\n          <div className=\"flex items-center gap-2 text-red-700 dark:text-red-300\">\n            <AlertCircle className=\"w-4 h-4\" />\n            <span className=\"text-sm\">{error}</span>\n          </div>\n        </div>\n      )}\n\n      {/* Main Header */}\n      <div className=\"h-16 flex items-center justify-between px-4\">\n        {/* Left Section */}\n        <div className=\"flex items-center gap-4\">\n        <Button\n          variant=\"ghost\"\n          size=\"sm\"\n          onClick={handleBackToDashboard}\n          className=\"flex items-center gap-2\"\n        >\n          <ArrowLeft className=\"w-4 h-4\" />\n          Back\n        </Button>\n        \n        <div className=\"flex items-center gap-2\">\n          <div className=\"w-6 h-6 bg-gradient-to-r from-blue-600 to-indigo-600 rounded\"></div>\n          <span className=\"font-semibold\">FlowUI</span>\n        </div>\n\n        <div className=\"text-sm text-muted-foreground\">\n          {currentProject?.name || 'Untitled Project'}\n        </div>\n      </div>\n\n      {/* Center Section - Tools */}\n      <div className=\"flex items-center gap-2\">\n        <Button\n          variant=\"ghost\"\n          size=\"sm\"\n          onClick={handleUndo}\n          disabled={!canUndo()}\n          className=\"flex items-center gap-1\"\n          title=\"Undo\"\n        >\n          <Undo className=\"w-4 h-4\" />\n        </Button>\n\n        <Button\n          variant=\"ghost\"\n          size=\"sm\"\n          onClick={handleRedo}\n          disabled={!canRedo()}\n          className=\"flex items-center gap-1\"\n          title=\"Redo\"\n        >\n          <Redo className=\"w-4 h-4\" />\n        </Button>\n\n        <div className=\"w-px h-6 bg-gray-300 dark:bg-gray-600 mx-2\" />\n\n        <Button\n          variant={isPreviewMode ? \"default\" : \"ghost\"}\n          size=\"sm\"\n          onClick={togglePreviewMode}\n          className=\"flex items-center gap-2\"\n        >\n          {isPreviewMode ? (\n            <>\n              <EyeOff className=\"w-4 h-4\" />\n              Edit\n            </>\n          ) : (\n            <>\n              <Eye className=\"w-4 h-4\" />\n              Preview\n            </>\n          )}\n        </Button>\n      </div>\n\n      {/* Right Section */}\n      <div className=\"flex items-center gap-2\">\n        <Badge variant=\"secondary\" className=\"text-xs\">\n          {currentProject?.components?.length || 0} components\n        </Badge>\n\n        <Button\n          variant=\"ghost\"\n          size=\"sm\"\n          onClick={handleSettings}\n          className=\"flex items-center gap-1\"\n        >\n          <Settings className=\"w-4 h-4\" />\n        </Button>\n\n        {/* Setup account button */}\n        <Button\n          variant=\"ghost\"\n          size=\"sm\"\n          onClick={handleSetupAccount}\n          className=\"flex items-center gap-1 text-xs\"\n          title=\"Setup Account\"\n        >\n          🔧\n        </Button>\n\n        {/* Temporary debug button */}\n        <Button\n          variant=\"ghost\"\n          size=\"sm\"\n          onClick={handleDebugUser}\n          className=\"flex items-center gap-1 text-xs\"\n          title=\"Debug User Info\"\n        >\n          🐛\n        </Button>\n\n        <Button\n          variant=\"ghost\"\n          size=\"sm\"\n          onClick={handleShare}\n          className=\"flex items-center gap-2\"\n        >\n          <Share className=\"w-4 h-4\" />\n          Share\n        </Button>\n\n        <Button\n          variant=\"outline\"\n          size=\"sm\"\n          onClick={handlePublish}\n          className=\"flex items-center gap-2\"\n        >\n          <Globe className=\"w-4 h-4\" />\n          Publish\n        </Button>\n\n        <Button\n          size=\"sm\"\n          onClick={handleSave}\n          disabled={isSaving}\n          className=\"flex items-center gap-2\"\n        >\n          <Save className=\"w-4 h-4\" />\n          {isSaving ? 'Saving...' : 'Save'}\n        </Button>\n        </div>\n      </div>\n    </header>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAPA;;;;;;;AAqBO,SAAS;QAqTL;;IApTT,MAAM,EACJ,cAAc,EACd,aAAa,EACb,QAAQ,EACR,KAAK,EACL,iBAAiB,EACjB,iBAAiB,EACjB,WAAW,EACX,aAAa,EACb,QAAQ,EACR,IAAI,EACJ,IAAI,EACJ,OAAO,EACP,OAAO,EACR,GAAG,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD;IACpB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,aAAa;QACjB,IAAI,CAAC,gBAAgB;QAErB,IAAI;YACF,mDAAmD;YACnD,MAAM,iBAAiB,MAAM,MAAM,yBAAyB;gBAC1D,QAAQ;YACV;YAEA,IAAI,CAAC,eAAe,EAAE,EAAE;gBACtB,MAAM,cAAc,MAAM,eAAe,IAAI;gBAC7C,MAAM,IAAI,MAAM,AAAC,iBAAkC,OAAlB,YAAY,KAAK;YACpD;YAEA,gDAAgD;YAChD,IAAI,eAAe,EAAE,KAAK,gBAAgB;gBACxC,QAAQ,GAAG,CAAC;gBACZ,MAAM,cAAc,eAAe,IAAI,EAAE,eAAe,WAAW;YACrE,OAAO;gBACL,QAAQ,GAAG,CAAC;gBACZ,MAAM;YACR;QAEA,0DAA0D;QAC5D,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,gBAAgB;YAC9B,mDAAmD;YACnD,IAAI,aAAkB,eAAe,AAAC,OAAe,QAAQ,EAAE;;gBAC3D,OAAe,QAAQ,CAAC;oBACxB,MAAM;oBACN,OAAO;oBACP,aAAa,eAAe,QAAQ,IAAI,OAAO,GAAG;gBACpD;YACF;QACF;IACF;IAEA,sCAAsC;IACtC,MAAM,kBAAkB;QACtB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,QAAQ,GAAG,CAAC,oBAAoB;YAChC,MAAM,AAAC,qBAAkD,OAA9B,KAAK,SAAS,CAAC,MAAM,MAAM;QACxD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;YAC/B,MAAM;QACR;IACF;IAEA,yBAAyB;IACzB,MAAM,qBAAqB;QACzB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,yBAAyB;gBACpD,QAAQ;YACV;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,IAAI,aAAkB,eAAe,AAAC,OAAe,QAAQ,EAAE;;oBAC3D,OAAe,QAAQ,CAAC;wBACxB,MAAM;wBACN,OAAO;wBACP,aAAa;oBACf;gBACF;YACF,OAAO;gBACL,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,IAAI,MAAM,UAAU,KAAK;YACjC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;YAC/B,IAAI,aAAkB,eAAe,AAAC,OAAe,QAAQ,EAAE;;gBAC3D,OAAe,QAAQ,CAAC;oBACxB,MAAM;oBACN,OAAO;oBACP,aAAa,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBACxD;YACF;QACF;IACF;IAEA,8BAA8B;IAC9B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,OAAO;gBACT,MAAM,QAAQ;qDAAW;wBACvB,SAAS;oBACX;oDAAG;gBACH;+CAAO,IAAM,aAAa;;YAC5B;QACF;kCAAG;QAAC;QAAO;KAAS;IAEpB,wDAAwD;IACxD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,MAAM;sDAAa;oBACjB,IAAI;wBACF,MAAM,WAAW,MAAM,MAAM,yBAAyB;4BACpD,QAAQ;wBACV;wBAEA,IAAI,CAAC,SAAS,EAAE,EAAE;4BAChB,MAAM,YAAY,MAAM,SAAS,IAAI;4BACrC,QAAQ,KAAK,CAAC,0BAA0B;wBAC1C,OAAO;4BACL,QAAQ,GAAG,CAAC;wBACd;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,wBAAwB;oBACxC;gBACF;;YAEA;QACF;kCAAG,EAAE;IAEL,MAAM,cAAc;QAClB,IAAI,CAAC,kBAAkB,eAAe,UAAU,CAAC,MAAM,KAAK,GAAG;YAC7D,MAAM;YACN;QACF;QAEA,4BAA4B;QAC5B,MAAM,cAAc;YAClB,MAAM,eAAe,IAAI;YACzB,YAAY,eAAe,UAAU;YACrC,WAAW,KAAK,GAAG;QACrB;QAEA,0BAA0B;QAC1B,MAAM,cAAc,KAAK,KAAK,SAAS,CAAC;QACxC,MAAM,WAAW,AAAC,GAAoC,OAAlC,OAAO,QAAQ,CAAC,MAAM,EAAC,aAAuB,OAAZ;QAEtD,oBAAoB;QACpB,UAAU,SAAS,CAAC,SAAS,CAAC,UAAU,IAAI,CAAC;YAC3C,MAAM,AAAC,yCAAiD,OAAT,UAAS;QAC1D,GAAG,KAAK,CAAC;YACP,8BAA8B;YAC9B,MAAM,WAAW,SAAS,aAAa,CAAC;YACxC,SAAS,KAAK,GAAG;YACjB,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,SAAS,MAAM;YACf,SAAS,WAAW,CAAC;YACrB,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,MAAM,AAAC,6BAAqC,OAAT,UAAS;QAC9C;IACF;IAEA,MAAM,gBAAgB;QACpB,IAAI,CAAC,kBAAkB,eAAe,UAAU,CAAC,MAAM,KAAK,GAAG;YAC7D,MAAM;YACN;QACF;QAEA,wBAAwB;QACxB,MAAM,cAAc;YAClB,MAAM,eAAe,IAAI;YACzB,YAAY,eAAe,UAAU;YACrC,WAAW,KAAK,GAAG;YACnB,WAAW;QACb;QAEA,0BAA0B;QAC1B,MAAM,cAAc,KAAK,KAAK,SAAS,CAAC;QACxC,MAAM,YAAY,AAAC,GAA8B,OAA5B,OAAO,QAAQ,CAAC,MAAM,EAAC,OAAiB,OAAZ;QAEjD,qCAAqC;QACrC,UAAU,SAAS,CAAC,SAAS,CAAC,WAAW,IAAI,CAAC;YAC5C,MAAM,AAAC,qDAA8D,OAAV,WAAU;QACvE,GAAG,KAAK,CAAC;YACP,8BAA8B;YAC9B,MAAM,WAAW,SAAS,aAAa,CAAC;YACxC,SAAS,KAAK,GAAG;YACjB,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,SAAS,MAAM;YACf,SAAS,WAAW,CAAC;YACrB,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,MAAM,AAAC,qDAA8D,OAAV,WAAU;QACvE;IACF;IAEA,MAAM,iBAAiB;QACrB,yCAAyC;QACzC,MAAM;IACR;IAEA,MAAM,aAAa;QACjB;IACF;IAEA,MAAM,aAAa;QACjB;IACF;IAEA,MAAM,wBAAwB;QAC5B,OAAO,IAAI,CAAC;IACd;IAEA,0CAA0C;IAC1C,IAAI,CAAC,gBAAgB;QACnB,kBAAkB;YAChB,IAAI;YACJ,MAAM;YACN,aAAa;YACb,cAAc;YACd,YAAY,EAAE;QAChB;IACF;IAEA,qBACE,6LAAC;QAAO,WAAU;;YAEf,uBACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,uNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;sCACvB,6LAAC;4BAAK,WAAU;sCAAW;;;;;;;;;;;;;;;;;0BAMjC,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACf,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,WAAU;;kDAEV,6LAAC,mNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;oCAAY;;;;;;;0CAInC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAK,WAAU;kDAAgB;;;;;;;;;;;;0CAGlC,6LAAC;gCAAI,WAAU;0CACZ,CAAA,2BAAA,qCAAA,eAAgB,IAAI,KAAI;;;;;;;;;;;;kCAK7B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,UAAU,CAAC;gCACX,WAAU;gCACV,OAAM;0CAEN,cAAA,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;0CAGlB,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,UAAU,CAAC;gCACX,WAAU;gCACV,OAAM;0CAEN,cAAA,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;0CAGlB,6LAAC;gCAAI,WAAU;;;;;;0CAEf,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAS,gBAAgB,YAAY;gCACrC,MAAK;gCACL,SAAS;gCACT,WAAU;0CAET,8BACC;;sDACE,6LAAC,6MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAY;;iEAIhC;;sDACE,6LAAC,mMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;wCAAY;;;;;;;;;;;;;;kCAQnC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,oIAAA,CAAA,QAAK;gCAAC,SAAQ;gCAAY,WAAU;;oCAClC,CAAA,2BAAA,sCAAA,6BAAA,eAAgB,UAAU,cAA1B,iDAAA,2BAA4B,MAAM,KAAI;oCAAE;;;;;;;0CAG3C,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,WAAU;0CAEV,cAAA,6LAAC,6MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;0CAItB,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,WAAU;gCACV,OAAM;0CACP;;;;;;0CAKD,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,WAAU;gCACV,OAAM;0CACP;;;;;;0CAID,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,WAAU;;kDAEV,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;oCAAY;;;;;;;0CAI/B,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,WAAU;;kDAEV,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;oCAAY;;;;;;;0CAI/B,6LAAC,qIAAA,CAAA,SAAM;gCACL,MAAK;gCACL,SAAS;gCACT,UAAU;gCACV,WAAU;;kDAEV,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCACf,WAAW,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;AAMpC;GAxXgB;;QAeV,sHAAA,CAAA,oBAAiB;QACN,qIAAA,CAAA,YAAS;;;KAhBV", "debugId": null}}, {"offset": {"line": 3767, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/FlowUi/src/components/ui/toast.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { CheckCircle, XCircle, AlertCircle, Info, X } from 'lucide-react'\nimport { cn } from '@/lib/utils'\n\nexport type ToastType = 'success' | 'error' | 'warning' | 'info'\n\nexport interface Toast {\n  id: string\n  type: ToastType\n  title: string\n  description?: string\n  duration?: number\n}\n\ninterface ToastProps {\n  toast: Toast\n  onClose: (id: string) => void\n}\n\nconst toastIcons = {\n  success: CheckCircle,\n  error: XCircle,\n  warning: AlertCircle,\n  info: Info,\n}\n\nconst toastStyles = {\n  success: 'bg-green-50 border-green-200 text-green-800 dark:bg-green-900/20 dark:border-green-800 dark:text-green-300',\n  error: 'bg-red-50 border-red-200 text-red-800 dark:bg-red-900/20 dark:border-red-800 dark:text-red-300',\n  warning: 'bg-yellow-50 border-yellow-200 text-yellow-800 dark:bg-yellow-900/20 dark:border-yellow-800 dark:text-yellow-300',\n  info: 'bg-blue-50 border-blue-200 text-blue-800 dark:bg-blue-900/20 dark:border-blue-800 dark:text-blue-300',\n}\n\nfunction ToastComponent({ toast, onClose }: ToastProps) {\n  const Icon = toastIcons[toast.type]\n\n  useEffect(() => {\n    const timer = setTimeout(() => {\n      onClose(toast.id)\n    }, toast.duration || 5000)\n\n    return () => clearTimeout(timer)\n  }, [toast.id, toast.duration, onClose])\n\n  return (\n    <div\n      className={cn(\n        'flex items-start gap-3 p-4 rounded-lg border shadow-lg transition-all duration-300 ease-in-out',\n        'animate-in slide-in-from-right-full',\n        toastStyles[toast.type]\n      )}\n    >\n      <Icon className=\"w-5 h-5 flex-shrink-0 mt-0.5\" />\n      <div className=\"flex-1 min-w-0\">\n        <p className=\"font-medium\">{toast.title}</p>\n        {toast.description && (\n          <p className=\"text-sm opacity-90 mt-1\">{toast.description}</p>\n        )}\n      </div>\n      <button\n        onClick={() => onClose(toast.id)}\n        className=\"flex-shrink-0 p-1 rounded-md hover:bg-black/10 dark:hover:bg-white/10 transition-colors\"\n      >\n        <X className=\"w-4 h-4\" />\n      </button>\n    </div>\n  )\n}\n\nexport function ToastContainer() {\n  const [toasts, setToasts] = useState<Toast[]>([])\n\n  const removeToast = (id: string) => {\n    setToasts(prev => prev.filter(toast => toast.id !== id))\n  }\n\n  const addToast = (toast: Omit<Toast, 'id'>) => {\n    const id = Math.random().toString(36).substr(2, 9)\n    setToasts(prev => [...prev, { ...toast, id }])\n  }\n\n  // Expose addToast globally\n  useEffect(() => {\n    ;(window as any).addToast = addToast\n    return () => {\n      delete (window as any).addToast\n    }\n  }, [])\n\n  return (\n    <div className=\"fixed top-4 right-4 z-50 flex flex-col gap-2 max-w-sm w-full\">\n      {toasts.map(toast => (\n        <ToastComponent\n          key={toast.id}\n          toast={toast}\n          onClose={removeToast}\n        />\n      ))}\n    </div>\n  )\n}\n\n// Helper functions for easy toast creation\nexport const toast = {\n  success: (title: string, description?: string) => {\n    if (typeof window !== 'undefined' && (window as any).addToast) {\n      ;(window as any).addToast({ type: 'success', title, description })\n    }\n  },\n  error: (title: string, description?: string) => {\n    if (typeof window !== 'undefined' && (window as any).addToast) {\n      ;(window as any).addToast({ type: 'error', title, description })\n    }\n  },\n  warning: (title: string, description?: string) => {\n    if (typeof window !== 'undefined' && (window as any).addToast) {\n      ;(window as any).addToast({ type: 'warning', title, description })\n    }\n  },\n  info: (title: string, description?: string) => {\n    if (typeof window !== 'undefined' && (window as any).addToast) {\n      ;(window as any).addToast({ type: 'info', title, description })\n    }\n  },\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;;;AAJA;;;;AAqBA,MAAM,aAAa;IACjB,SAAS,8NAAA,CAAA,cAAW;IACpB,OAAO,+MAAA,CAAA,UAAO;IACd,SAAS,uNAAA,CAAA,cAAW;IACpB,MAAM,qMAAA,CAAA,OAAI;AACZ;AAEA,MAAM,cAAc;IAClB,SAAS;IACT,OAAO;IACP,SAAS;IACT,MAAM;AACR;AAEA,SAAS,eAAe,KAA8B;QAA9B,EAAE,KAAK,EAAE,OAAO,EAAc,GAA9B;;IACtB,MAAM,OAAO,UAAU,CAAC,MAAM,IAAI,CAAC;IAEnC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,MAAM,QAAQ;kDAAW;oBACvB,QAAQ,MAAM,EAAE;gBAClB;iDAAG,MAAM,QAAQ,IAAI;YAErB;4CAAO,IAAM,aAAa;;QAC5B;mCAAG;QAAC,MAAM,EAAE;QAAE,MAAM,QAAQ;QAAE;KAAQ;IAEtC,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kGACA,uCACA,WAAW,CAAC,MAAM,IAAI,CAAC;;0BAGzB,6LAAC;gBAAK,WAAU;;;;;;0BAChB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAE,WAAU;kCAAe,MAAM,KAAK;;;;;;oBACtC,MAAM,WAAW,kBAChB,6LAAC;wBAAE,WAAU;kCAA2B,MAAM,WAAW;;;;;;;;;;;;0BAG7D,6LAAC;gBACC,SAAS,IAAM,QAAQ,MAAM,EAAE;gBAC/B,WAAU;0BAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAIrB;GAlCS;KAAA;AAoCF,SAAS;;IACd,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW,EAAE;IAEhD,MAAM,cAAc,CAAC;QACnB,UAAU,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;IACtD;IAEA,MAAM,WAAW,CAAC;QAChB,MAAM,KAAK,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;QAChD,UAAU,CAAA,OAAQ;mBAAI;gBAAM;oBAAE,GAAG,KAAK;oBAAE;gBAAG;aAAE;IAC/C;IAEA,2BAA2B;IAC3B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;;YACN,OAAe,QAAQ,GAAG;YAC5B;4CAAO;oBACL,OAAO,AAAC,OAAe,QAAQ;gBACjC;;QACF;mCAAG,EAAE;IAEL,qBACE,6LAAC;QAAI,WAAU;kBACZ,OAAO,GAAG,CAAC,CAAA,sBACV,6LAAC;gBAEC,OAAO;gBACP,SAAS;eAFJ,MAAM,EAAE;;;;;;;;;;AAOvB;IA/BgB;MAAA;AAkCT,MAAM,QAAQ;IACnB,SAAS,CAAC,OAAe;QACvB,IAAI,aAAkB,eAAe,AAAC,OAAe,QAAQ,EAAE;;YAC3D,OAAe,QAAQ,CAAC;gBAAE,MAAM;gBAAW;gBAAO;YAAY;QAClE;IACF;IACA,OAAO,CAAC,OAAe;QACrB,IAAI,aAAkB,eAAe,AAAC,OAAe,QAAQ,EAAE;;YAC3D,OAAe,QAAQ,CAAC;gBAAE,MAAM;gBAAS;gBAAO;YAAY;QAChE;IACF;IACA,SAAS,CAAC,OAAe;QACvB,IAAI,aAAkB,eAAe,AAAC,OAAe,QAAQ,EAAE;;YAC3D,OAAe,QAAQ,CAAC;gBAAE,MAAM;gBAAW;gBAAO;YAAY;QAClE;IACF;IACA,MAAM,CAAC,OAAe;QACpB,IAAI,aAAkB,eAAe,AAAC,OAAe,QAAQ,EAAE;;YAC3D,OAAe,QAAQ,CAAC;gBAAE,MAAM;gBAAQ;gBAAO;YAAY;QAC/D;IACF;AACF", "debugId": null}}, {"offset": {"line": 3976, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/FlowUi/src/components/builder/ui-builder.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport {\n  DndContext,\n  DragEndEvent,\n  DragOverlay,\n  DragStartEvent,\n  closestCenter,\n  PointerSensor,\n  KeyboardSensor,\n  TouchSensor,\n  MouseSensor,\n  useSensor,\n  useSensors\n} from '@dnd-kit/core'\nimport {\n  restrictToWindowEdges,\n  restrictToParentElement,\n  snapCenterToCursor\n} from '@dnd-kit/modifiers'\nimport { Button } from '@/components/ui/button'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport { ComponentPalette } from './component-palette'\nimport { BuilderCanvas } from './builder-canvas'\nimport { PropertyPanel } from './property-panel'\nimport { BuilderHeader } from './builder-header'\nimport { useUIBuilderStore } from '@/lib/store'\nimport { UIComponent } from '@/types'\nimport { generateId } from '@/lib/utils'\nimport { ToastContainer } from '@/components/ui/toast'\n\nexport function UIBuilder() {\n  const {\n    currentProject,\n    selectedComponent,\n    draggedComponent,\n    isPreviewMode,\n    setDraggedComponent,\n    addComponent,\n    updateComponent,\n    addComponentWithHistory,\n    updateComponentWithHistory,\n  } = useUIBuilderStore()\n\n  const [activeId, setActiveId] = useState<string | null>(null)\n  const [snapToGrid, setSnapToGrid] = useState(true)\n  const gridSize = 10 // 10px grid\n\n  // Custom snap-to-grid modifier\n  const snapToGridModifier = ({ transform }) => {\n    if (!snapToGrid) return transform\n\n    return {\n      ...transform,\n      x: Math.round(transform.x / gridSize) * gridSize,\n      y: Math.round(transform.y / gridSize) * gridSize,\n    }\n  }\n\n  // Configure sensors for better drag and drop experience\n  const sensors = useSensors(\n    // Mouse sensor for desktop\n    useSensor(MouseSensor, {\n      activationConstraint: {\n        distance: 8, // Require 8px of movement before activating\n      },\n    }),\n    // Touch sensor for mobile devices\n    useSensor(TouchSensor, {\n      activationConstraint: {\n        delay: 200, // 200ms delay before activation\n        tolerance: 8, // Allow 8px of movement during delay\n      },\n    }),\n    // Keyboard sensor for accessibility\n    useSensor(KeyboardSensor, {\n      coordinateGetter: (event, { context: { active, droppableRects, droppableContainers, collisionRect } }) => {\n        // Custom keyboard navigation logic could be added here\n        return undefined\n      },\n    })\n  )\n\n  const handleDragStart = (event: DragStartEvent) => {\n    const { active } = event\n    setActiveId(active.id as string)\n    \n    // If dragging from palette, create a new component\n    if (active.data.current?.fromPalette) {\n      const componentType = active.data.current.componentType\n      const newComponent: UIComponent = {\n        id: generateId(),\n        project_id: currentProject?.id || '',\n        type: componentType,\n        props: getDefaultProps(componentType),\n        position: { x: 0, y: 0 },\n        size: { width: 200, height: 50 },\n        created_at: new Date().toISOString(),\n        updated_at: new Date().toISOString(),\n      }\n      setDraggedComponent(newComponent)\n    }\n  }\n\n  const handleDragEnd = (event: DragEndEvent) => {\n    const { active, over } = event\n    setActiveId(null)\n    setDraggedComponent(null)\n\n    if (!over) return\n\n    // If dropping on canvas from palette\n    if (over.id === 'canvas' && draggedComponent && active.id.toString().startsWith('palette-')) {\n      // Get the canvas element to calculate relative position\n      const canvasElement = document.querySelector('[data-canvas=\"true\"]')\n      if (canvasElement) {\n        const canvasRect = canvasElement.getBoundingClientRect()\n\n        // Calculate position based on where the drag ended\n        let x = event.activatorEvent.clientX - canvasRect.left - (draggedComponent.size.width / 2)\n        let y = event.activatorEvent.clientY - canvasRect.top - (draggedComponent.size.height / 2)\n\n        // Apply snap to grid if enabled\n        if (snapToGrid) {\n          x = Math.round(x / gridSize) * gridSize\n          y = Math.round(y / gridSize) * gridSize\n        }\n\n        // Ensure component stays within canvas bounds\n        x = Math.max(0, Math.min(x, canvasRect.width - draggedComponent.size.width))\n        y = Math.max(0, Math.min(y, canvasRect.height - draggedComponent.size.height))\n\n        addComponentWithHistory({\n          ...draggedComponent,\n          position: { x, y },\n        })\n      } else {\n        // Fallback to simple positioning\n        addComponent({\n          ...draggedComponent,\n          position: { x: 50, y: 50 },\n        })\n      }\n    }\n\n    // If moving an existing component on canvas\n    if (over.id === 'canvas' && !active.id.toString().startsWith('palette-') && event.delta) {\n      const componentId = active.id.toString()\n      const component = currentProject?.components.find(c => c.id === componentId)\n\n      if (component) {\n        let newX = component.position.x + event.delta.x\n        let newY = component.position.y + event.delta.y\n\n        // Apply snap to grid if enabled\n        if (snapToGrid) {\n          newX = Math.round(newX / gridSize) * gridSize\n          newY = Math.round(newY / gridSize) * gridSize\n        }\n\n        // Ensure component stays within canvas bounds\n        const canvasElement = document.querySelector('[data-canvas=\"true\"]')\n        if (canvasElement) {\n          const canvasRect = canvasElement.getBoundingClientRect()\n          newX = Math.max(0, Math.min(newX, canvasRect.width - component.size.width))\n          newY = Math.max(0, Math.min(newY, canvasRect.height - component.size.height))\n        }\n\n        updateComponentWithHistory(componentId, {\n          position: { x: newX, y: newY },\n          updated_at: new Date().toISOString()\n        })\n      }\n    }\n  }\n\n  const getDefaultProps = (componentType: string) => {\n    switch (componentType) {\n      case 'button':\n        return { text: 'Button', variant: 'default', size: 'md' }\n      case 'input':\n        return { placeholder: 'Enter text...', type: 'text' }\n      case 'text':\n        return { content: 'Text content', fontSize: 'base' }\n      case 'heading':\n        return { content: 'Heading', level: 1 }\n      default:\n        return {}\n    }\n  }\n\n  const renderPreviewComponent = (component: UIComponent) => {\n    switch (component.type) {\n      case 'button':\n        return (\n          <Button\n            variant={component.props.variant || 'default'}\n            size={component.props.size || 'default'}\n            className=\"w-full\"\n          >\n            {component.props.text || 'Button'}\n          </Button>\n        )\n\n      case 'input':\n        return (\n          <input\n            placeholder={component.props.placeholder || 'Enter text...'}\n            type={component.props.type || 'text'}\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n          />\n        )\n\n      case 'textarea':\n        return (\n          <textarea\n            placeholder={component.props.placeholder || 'Enter text...'}\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n            rows={component.props.rows || 3}\n          />\n        )\n\n      case 'text':\n        return (\n          <p\n            className={`text-${component.props.fontSize || 'base'} ${component.props.className || ''}`}\n            style={{ color: component.props.color }}\n          >\n            {component.props.content || 'Text content'}\n          </p>\n        )\n\n      case 'heading':\n        const HeadingTag = `h${component.props.level || 1}` as keyof JSX.IntrinsicElements\n        return (\n          <HeadingTag\n            className={`font-bold ${component.props.className || ''}`}\n            style={{ color: component.props.color }}\n          >\n            {component.props.content || 'Heading'}\n          </HeadingTag>\n        )\n\n      case 'card':\n        return (\n          <Card className=\"w-full\">\n            <CardHeader>\n              <CardTitle>{component.props.title || 'Card Title'}</CardTitle>\n            </CardHeader>\n            <CardContent>\n              <p>{component.props.content || 'Card content goes here...'}</p>\n            </CardContent>\n          </Card>\n        )\n\n      case 'checkbox':\n        return (\n          <div className=\"flex items-center space-x-2\">\n            <input type=\"checkbox\" id={component.id} className=\"rounded\" />\n            <label htmlFor={component.id}>\n              {component.props.label || 'Checkbox label'}\n            </label>\n          </div>\n        )\n\n      default:\n        return (\n          <div className=\"border border-gray-300 rounded p-2 bg-gray-100\">\n            <Badge variant=\"secondary\">{component.type}</Badge>\n          </div>\n        )\n    }\n  }\n\n  if (isPreviewMode) {\n    return (\n      <div className=\"h-screen bg-white\">\n        <BuilderHeader />\n        <div className=\"h-full pt-16 overflow-auto\">\n          {/* Preview mode content - render actual components */}\n          <div className=\"h-full p-4\">\n            <div className=\"max-w-4xl mx-auto bg-white min-h-[600px] relative\">\n              {currentProject?.components && currentProject.components.length > 0 ? (\n                currentProject.components.map((component) => (\n                  <div\n                    key={component.id}\n                    style={{\n                      position: 'absolute',\n                      left: component.position.x,\n                      top: component.position.y,\n                      width: component.size.width,\n                      minHeight: component.size.height,\n                    }}\n                  >\n                    {renderPreviewComponent(component)}\n                  </div>\n                ))\n              ) : (\n                <div className=\"h-full flex items-center justify-center\">\n                  <Card className=\"p-8\">\n                    <h2 className=\"text-2xl font-bold mb-4\">Preview Mode</h2>\n                    <p className=\"text-muted-foreground\">\n                      Add components to see them rendered here\n                    </p>\n                  </Card>\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n      </div>\n    )\n  }\n\n  // Render drag preview based on what's being dragged\n  const renderDragOverlay = () => {\n    if (!activeId) return null\n\n    // If dragging from palette, show the component type\n    if (activeId.startsWith('palette-')) {\n      const componentType = activeId.replace('palette-', '')\n      return (\n        <div className=\"bg-white dark:bg-gray-800 border-2 border-blue-500 rounded-lg p-3 shadow-lg opacity-90\">\n          <div className=\"flex items-center gap-2\">\n            <div className=\"w-2 h-2 bg-blue-500 rounded-full\"></div>\n            <span className=\"font-medium text-sm capitalize\">{componentType}</span>\n          </div>\n        </div>\n      )\n    }\n\n    // If dragging an existing component, show its current state\n    if (draggedComponent) {\n      return (\n        <div\n          className=\"bg-white dark:bg-gray-800 border-2 border-blue-500 rounded-lg shadow-lg opacity-90\"\n          style={{\n            width: draggedComponent.size.width,\n            minHeight: draggedComponent.size.height,\n          }}\n        >\n          <div className=\"p-2\">\n            <Badge variant=\"secondary\" className=\"text-xs\">\n              {draggedComponent.type}\n            </Badge>\n          </div>\n        </div>\n      )\n    }\n\n    return null\n  }\n\n  return (\n    <DndContext\n      sensors={sensors}\n      collisionDetection={closestCenter}\n      onDragStart={handleDragStart}\n      onDragEnd={handleDragEnd}\n    >\n      <div className=\"h-screen bg-gray-50 dark:bg-gray-900 flex flex-col\">\n        <BuilderHeader />\n        \n        <div className=\"flex-1 flex overflow-hidden\">\n          {/* Component Palette */}\n          <div className=\"w-64 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 overflow-y-auto\">\n            <ComponentPalette />\n          </div>\n\n          {/* Main Canvas Area */}\n          <div className=\"flex-1 flex flex-col\">\n            <div className=\"flex-1 overflow-auto\">\n              <BuilderCanvas />\n            </div>\n          </div>\n\n          {/* Property Panel */}\n          <div className=\"w-80 bg-white dark:bg-gray-800 border-l border-gray-200 dark:border-gray-700 overflow-y-auto\">\n            <PropertyPanel />\n          </div>\n        </div>\n      </div>\n\n      <DragOverlay\n        modifiers={[restrictToWindowEdges, snapToGridModifier]}\n        dropAnimation={{\n          duration: 300,\n          easing: 'cubic-bezier(0.18, 0.67, 0.6, 1.22)',\n        }}\n      >\n        {renderDragOverlay()}\n      </DragOverlay>\n\n      <ToastContainer />\n    </DndContext>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAaA;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;;;AA/BA;;;;;;;;;;;;;;AAiCO,SAAS;;IACd,MAAM,EACJ,cAAc,EACd,iBAAiB,EACjB,gBAAgB,EAChB,aAAa,EACb,mBAAmB,EACnB,YAAY,EACZ,eAAe,EACf,uBAAuB,EACvB,0BAA0B,EAC3B,GAAG,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD;IAEpB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IACxD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,WAAW,GAAG,YAAY;;IAEhC,+BAA+B;IAC/B,MAAM,qBAAqB;YAAC,EAAE,SAAS,EAAE;QACvC,IAAI,CAAC,YAAY,OAAO;QAExB,OAAO;YACL,GAAG,SAAS;YACZ,GAAG,KAAK,KAAK,CAAC,UAAU,CAAC,GAAG,YAAY;YACxC,GAAG,KAAK,KAAK,CAAC,UAAU,CAAC,GAAG,YAAY;QAC1C;IACF;IAEA,wDAAwD;IACxD,MAAM,UAAU,CAAA,GAAA,8JAAA,CAAA,aAAU,AAAD,EACvB,2BAA2B;IAC3B,CAAA,GAAA,8JAAA,CAAA,YAAS,AAAD,EAAE,8JAAA,CAAA,cAAW,EAAE;QACrB,sBAAsB;YACpB,UAAU;QACZ;IACF,IACA,kCAAkC;IAClC,CAAA,GAAA,8JAAA,CAAA,YAAS,AAAD,EAAE,8JAAA,CAAA,cAAW,EAAE;QACrB,sBAAsB;YACpB,OAAO;YACP,WAAW;QACb;IACF,IACA,oCAAoC;IACpC,CAAA,GAAA,8JAAA,CAAA,YAAS,AAAD,EAAE,8JAAA,CAAA,iBAAc,EAAE;QACxB,gBAAgB;6CAAE,CAAC;oBAAO,EAAE,SAAS,EAAE,MAAM,EAAE,cAAc,EAAE,mBAAmB,EAAE,aAAa,EAAE,EAAE;gBACnG,uDAAuD;gBACvD,OAAO;YACT;;IACF;IAGF,MAAM,kBAAkB,CAAC;YAKnB;QAJJ,MAAM,EAAE,MAAM,EAAE,GAAG;QACnB,YAAY,OAAO,EAAE;QAErB,mDAAmD;QACnD,KAAI,uBAAA,OAAO,IAAI,CAAC,OAAO,cAAnB,2CAAA,qBAAqB,WAAW,EAAE;YACpC,MAAM,gBAAgB,OAAO,IAAI,CAAC,OAAO,CAAC,aAAa;YACvD,MAAM,eAA4B;gBAChC,IAAI,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD;gBACb,YAAY,CAAA,2BAAA,qCAAA,eAAgB,EAAE,KAAI;gBAClC,MAAM;gBACN,OAAO,gBAAgB;gBACvB,UAAU;oBAAE,GAAG;oBAAG,GAAG;gBAAE;gBACvB,MAAM;oBAAE,OAAO;oBAAK,QAAQ;gBAAG;gBAC/B,YAAY,IAAI,OAAO,WAAW;gBAClC,YAAY,IAAI,OAAO,WAAW;YACpC;YACA,oBAAoB;QACtB;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG;QACzB,YAAY;QACZ,oBAAoB;QAEpB,IAAI,CAAC,MAAM;QAEX,qCAAqC;QACrC,IAAI,KAAK,EAAE,KAAK,YAAY,oBAAoB,OAAO,EAAE,CAAC,QAAQ,GAAG,UAAU,CAAC,aAAa;YAC3F,wDAAwD;YACxD,MAAM,gBAAgB,SAAS,aAAa,CAAC;YAC7C,IAAI,eAAe;gBACjB,MAAM,aAAa,cAAc,qBAAqB;gBAEtD,mDAAmD;gBACnD,IAAI,IAAI,MAAM,cAAc,CAAC,OAAO,GAAG,WAAW,IAAI,GAAI,iBAAiB,IAAI,CAAC,KAAK,GAAG;gBACxF,IAAI,IAAI,MAAM,cAAc,CAAC,OAAO,GAAG,WAAW,GAAG,GAAI,iBAAiB,IAAI,CAAC,MAAM,GAAG;gBAExF,gCAAgC;gBAChC,IAAI,YAAY;oBACd,IAAI,KAAK,KAAK,CAAC,IAAI,YAAY;oBAC/B,IAAI,KAAK,KAAK,CAAC,IAAI,YAAY;gBACjC;gBAEA,8CAA8C;gBAC9C,IAAI,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,WAAW,KAAK,GAAG,iBAAiB,IAAI,CAAC,KAAK;gBAC1E,IAAI,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,WAAW,MAAM,GAAG,iBAAiB,IAAI,CAAC,MAAM;gBAE5E,wBAAwB;oBACtB,GAAG,gBAAgB;oBACnB,UAAU;wBAAE;wBAAG;oBAAE;gBACnB;YACF,OAAO;gBACL,iCAAiC;gBACjC,aAAa;oBACX,GAAG,gBAAgB;oBACnB,UAAU;wBAAE,GAAG;wBAAI,GAAG;oBAAG;gBAC3B;YACF;QACF;QAEA,4CAA4C;QAC5C,IAAI,KAAK,EAAE,KAAK,YAAY,CAAC,OAAO,EAAE,CAAC,QAAQ,GAAG,UAAU,CAAC,eAAe,MAAM,KAAK,EAAE;YACvF,MAAM,cAAc,OAAO,EAAE,CAAC,QAAQ;YACtC,MAAM,YAAY,2BAAA,qCAAA,eAAgB,UAAU,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;YAEhE,IAAI,WAAW;gBACb,IAAI,OAAO,UAAU,QAAQ,CAAC,CAAC,GAAG,MAAM,KAAK,CAAC,CAAC;gBAC/C,IAAI,OAAO,UAAU,QAAQ,CAAC,CAAC,GAAG,MAAM,KAAK,CAAC,CAAC;gBAE/C,gCAAgC;gBAChC,IAAI,YAAY;oBACd,OAAO,KAAK,KAAK,CAAC,OAAO,YAAY;oBACrC,OAAO,KAAK,KAAK,CAAC,OAAO,YAAY;gBACvC;gBAEA,8CAA8C;gBAC9C,MAAM,gBAAgB,SAAS,aAAa,CAAC;gBAC7C,IAAI,eAAe;oBACjB,MAAM,aAAa,cAAc,qBAAqB;oBACtD,OAAO,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,MAAM,WAAW,KAAK,GAAG,UAAU,IAAI,CAAC,KAAK;oBACzE,OAAO,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,MAAM,WAAW,MAAM,GAAG,UAAU,IAAI,CAAC,MAAM;gBAC7E;gBAEA,2BAA2B,aAAa;oBACtC,UAAU;wBAAE,GAAG;wBAAM,GAAG;oBAAK;oBAC7B,YAAY,IAAI,OAAO,WAAW;gBACpC;YACF;QACF;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,OAAQ;YACN,KAAK;gBACH,OAAO;oBAAE,MAAM;oBAAU,SAAS;oBAAW,MAAM;gBAAK;YAC1D,KAAK;gBACH,OAAO;oBAAE,aAAa;oBAAiB,MAAM;gBAAO;YACtD,KAAK;gBACH,OAAO;oBAAE,SAAS;oBAAgB,UAAU;gBAAO;YACrD,KAAK;gBACH,OAAO;oBAAE,SAAS;oBAAW,OAAO;gBAAE;YACxC;gBACE,OAAO,CAAC;QACZ;IACF;IAEA,MAAM,yBAAyB,CAAC;QAC9B,OAAQ,UAAU,IAAI;YACpB,KAAK;gBACH,qBACE,6LAAC,qIAAA,CAAA,SAAM;oBACL,SAAS,UAAU,KAAK,CAAC,OAAO,IAAI;oBACpC,MAAM,UAAU,KAAK,CAAC,IAAI,IAAI;oBAC9B,WAAU;8BAET,UAAU,KAAK,CAAC,IAAI,IAAI;;;;;;YAI/B,KAAK;gBACH,qBACE,6LAAC;oBACC,aAAa,UAAU,KAAK,CAAC,WAAW,IAAI;oBAC5C,MAAM,UAAU,KAAK,CAAC,IAAI,IAAI;oBAC9B,WAAU;;;;;;YAIhB,KAAK;gBACH,qBACE,6LAAC;oBACC,aAAa,UAAU,KAAK,CAAC,WAAW,IAAI;oBAC5C,WAAU;oBACV,MAAM,UAAU,KAAK,CAAC,IAAI,IAAI;;;;;;YAIpC,KAAK;gBACH,qBACE,6LAAC;oBACC,WAAW,AAAC,QAA6C,OAAtC,UAAU,KAAK,CAAC,QAAQ,IAAI,QAAO,KAAmC,OAAhC,UAAU,KAAK,CAAC,SAAS,IAAI;oBACtF,OAAO;wBAAE,OAAO,UAAU,KAAK,CAAC,KAAK;oBAAC;8BAErC,UAAU,KAAK,CAAC,OAAO,IAAI;;;;;;YAIlC,KAAK;gBACH,MAAM,aAAa,AAAC,IAA8B,OAA3B,UAAU,KAAK,CAAC,KAAK,IAAI;gBAChD,qBACE,6LAAC;oBACC,WAAW,AAAC,aAA4C,OAAhC,UAAU,KAAK,CAAC,SAAS,IAAI;oBACrD,OAAO;wBAAE,OAAO,UAAU,KAAK,CAAC,KAAK;oBAAC;8BAErC,UAAU,KAAK,CAAC,OAAO,IAAI;;;;;;YAIlC,KAAK;gBACH,qBACE,6LAAC,mIAAA,CAAA,OAAI;oBAAC,WAAU;;sCACd,6LAAC,mIAAA,CAAA,aAAU;sCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;0CAAE,UAAU,KAAK,CAAC,KAAK,IAAI;;;;;;;;;;;sCAEvC,6LAAC,mIAAA,CAAA,cAAW;sCACV,cAAA,6LAAC;0CAAG,UAAU,KAAK,CAAC,OAAO,IAAI;;;;;;;;;;;;;;;;;YAKvC,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAM,MAAK;4BAAW,IAAI,UAAU,EAAE;4BAAE,WAAU;;;;;;sCACnD,6LAAC;4BAAM,SAAS,UAAU,EAAE;sCACzB,UAAU,KAAK,CAAC,KAAK,IAAI;;;;;;;;;;;;YAKlC;gBACE,qBACE,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;wBAAC,SAAQ;kCAAa,UAAU,IAAI;;;;;;;;;;;QAGlD;IACF;IAEA,IAAI,eAAe;QACjB,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC,qJAAA,CAAA,gBAAa;;;;;8BACd,6LAAC;oBAAI,WAAU;8BAEb,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACZ,CAAA,2BAAA,qCAAA,eAAgB,UAAU,KAAI,eAAe,UAAU,CAAC,MAAM,GAAG,IAChE,eAAe,UAAU,CAAC,GAAG,CAAC,CAAC,0BAC7B,6LAAC;oCAEC,OAAO;wCACL,UAAU;wCACV,MAAM,UAAU,QAAQ,CAAC,CAAC;wCAC1B,KAAK,UAAU,QAAQ,CAAC,CAAC;wCACzB,OAAO,UAAU,IAAI,CAAC,KAAK;wCAC3B,WAAW,UAAU,IAAI,CAAC,MAAM;oCAClC;8CAEC,uBAAuB;mCATnB,UAAU,EAAE;;;;0DAarB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;oCAAC,WAAU;;sDACd,6LAAC;4CAAG,WAAU;sDAA0B;;;;;;sDACxC,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAWvD;IAEA,oDAAoD;IACpD,MAAM,oBAAoB;QACxB,IAAI,CAAC,UAAU,OAAO;QAEtB,oDAAoD;QACpD,IAAI,SAAS,UAAU,CAAC,aAAa;YACnC,MAAM,gBAAgB,SAAS,OAAO,CAAC,YAAY;YACnD,qBACE,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAK,WAAU;sCAAkC;;;;;;;;;;;;;;;;;QAI1D;QAEA,4DAA4D;QAC5D,IAAI,kBAAkB;YACpB,qBACE,6LAAC;gBACC,WAAU;gBACV,OAAO;oBACL,OAAO,iBAAiB,IAAI,CAAC,KAAK;oBAClC,WAAW,iBAAiB,IAAI,CAAC,MAAM;gBACzC;0BAEA,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;wBAAC,SAAQ;wBAAY,WAAU;kCAClC,iBAAiB,IAAI;;;;;;;;;;;;;;;;QAKhC;QAEA,OAAO;IACT;IAEA,qBACE,6LAAC,8JAAA,CAAA,aAAU;QACT,SAAS;QACT,oBAAoB,8JAAA,CAAA,gBAAa;QACjC,aAAa;QACb,WAAW;;0BAEX,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,qJAAA,CAAA,gBAAa;;;;;kCAEd,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,wJAAA,CAAA,mBAAgB;;;;;;;;;;0CAInB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,qJAAA,CAAA,gBAAa;;;;;;;;;;;;;;;0CAKlB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,qJAAA,CAAA,gBAAa;;;;;;;;;;;;;;;;;;;;;;0BAKpB,6LAAC,8JAAA,CAAA,cAAW;gBACV,WAAW;oBAAC,wKAAA,CAAA,wBAAqB;oBAAE;iBAAmB;gBACtD,eAAe;oBACb,UAAU;oBACV,QAAQ;gBACV;0BAEC;;;;;;0BAGH,6LAAC,oIAAA,CAAA,iBAAc;;;;;;;;;;;AAGrB;GA7WgB;;QAWV,sHAAA,CAAA,oBAAiB;QAkBL,8JAAA,CAAA,aAAU;;;KA7BZ", "debugId": null}}, {"offset": {"line": 4578, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/FlowUi/src/app/builder/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useAuth } from '@/hooks/useAuth'\nimport { Loading } from '@/components/loading'\nimport { useRouter } from 'next/navigation'\nimport { useEffect } from 'react'\nimport { UIBuilder } from '@/components/builder/ui-builder'\n\nexport default function BuilderPage() {\n  const { user, isLoading, isAuthenticated } = useAuth()\n  const router = useRouter()\n\n  // Temporarily disable auth check for testing\n  // useEffect(() => {\n  //   if (!isLoading && !isAuthenticated) {\n  //     router.push('/auth/signin')\n  //   }\n  // }, [isLoading, isAuthenticated, router])\n\n  // if (isLoading) {\n  //   return <Loading />\n  // }\n\n  // if (!isAuthenticated) {\n  //   return null // Will redirect to signin\n  // }\n\n  return <UIBuilder />\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAEA;;;AANA;;;;AAQe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,UAAO,AAAD;IACnD,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,6CAA6C;IAC7C,oBAAoB;IACpB,0CAA0C;IAC1C,kCAAkC;IAClC,MAAM;IACN,2CAA2C;IAE3C,mBAAmB;IACnB,uBAAuB;IACvB,IAAI;IAEJ,0BAA0B;IAC1B,2CAA2C;IAC3C,IAAI;IAEJ,qBAAO,6LAAC,iJAAA,CAAA,YAAS;;;;;AACnB;GApBwB;;QACuB,0HAAA,CAAA,UAAO;QACrC,qIAAA,CAAA,YAAS;;;KAFF", "debugId": null}}]}