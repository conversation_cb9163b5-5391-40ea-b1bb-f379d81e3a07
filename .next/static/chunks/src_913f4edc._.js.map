{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/FlowUi/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\nimport { createBrowserClient, createServerClient } from '@supabase/ssr'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n\n// Client-side Supabase client\nexport const createClientComponentClient = () => {\n  return createBrowserClient(supabaseUrl, supabaseAnonKey)\n}\n\n// Legacy client for backward compatibility (client-side only)\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey)\n\n// Database types (will be generated later)\nexport type Database = {\n  public: {\n    Tables: {\n      users: {\n        Row: {\n          id: string\n          email: string\n          created_at: string\n          subscription_tier: string\n        }\n        Insert: {\n          id?: string\n          email: string\n          created_at?: string\n          subscription_tier?: string\n        }\n        Update: {\n          id?: string\n          email?: string\n          created_at?: string\n          subscription_tier?: string\n        }\n      }\n      // More tables will be added as we implement them\n    }\n  }\n}\n"], "names": [], "mappings": ";;;;AAGoB;AAHpB;AACA;AAAA;;;AAEA,MAAM;AACN,MAAM;AAGC,MAAM,8BAA8B;IACzC,OAAO,CAAA,GAAA,6KAAA,CAAA,sBAAmB,AAAD,EAAE,aAAa;AAC1C;AAGO,MAAM,WAAW,CAAA,GAAA,0LAAA,CAAA,eAAY,AAAD,EAAE,aAAa", "debugId": null}}, {"offset": {"line": 32, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/FlowUi/src/lib/store.ts"], "sourcesContent": ["import { create } from 'zustand'\nimport { devtools, persist } from 'zustand/middleware'\nimport { createClientComponentClient } from './supabase'\n\n// Command pattern for undo/redo\ninterface Command {\n  execute: () => void\n  undo: () => void\n  description: string\n}\n\n// Command implementations\nclass AddComponentCommand implements Command {\n  constructor(\n    private component: UIComponent,\n    private store: any\n  ) {}\n\n  execute() {\n    this.store.getState().addComponent(this.component)\n  }\n\n  undo() {\n    this.store.getState().removeComponent(this.component.id)\n  }\n\n  get description() {\n    return `Add ${this.component.type} component`\n  }\n}\n\nclass RemoveComponentCommand implements Command {\n  constructor(\n    private component: UIComponent,\n    private store: any\n  ) {}\n\n  execute() {\n    this.store.getState().removeComponent(this.component.id)\n  }\n\n  undo() {\n    this.store.getState().addComponent(this.component)\n  }\n\n  get description() {\n    return `Remove ${this.component.type} component`\n  }\n}\n\nclass UpdateComponentCommand implements Command {\n  constructor(\n    private componentId: string,\n    private oldProps: Partial<UIComponent>,\n    private newProps: Partial<UIComponent>,\n    private store: any\n  ) {}\n\n  execute() {\n    this.store.getState().updateComponent(this.componentId, this.newProps)\n  }\n\n  undo() {\n    this.store.getState().updateComponent(this.componentId, this.oldProps)\n  }\n\n  get description() {\n    return `Update component ${this.componentId}`\n  }\n}\n\n// User and Authentication Store\ninterface User {\n  id: string\n  email: string\n  subscription_tier: 'free' | 'pro' | 'team' | 'enterprise'\n}\n\ninterface AuthState {\n  user: User | null\n  isLoading: boolean\n  setUser: (user: User | null) => void\n  setLoading: (loading: boolean) => void\n  logout: () => void\n}\n\nexport const useAuthStore = create<AuthState>()(\n  devtools(\n    (set) => ({\n      user: null,\n      isLoading: true,\n      setUser: (user) => set({ user }),\n      setLoading: (isLoading) => set({ isLoading }),\n      logout: () => set({ user: null }),\n    }),\n    { name: 'auth-store' }\n  )\n)\n\n// UI Builder Store\ninterface UIComponent {\n  id: string\n  type: string\n  props: Record<string, any>\n  position: { x: number; y: number }\n  size: { width: number; height: number }\n  parent_id?: string\n  children?: string[]\n}\n\ninterface Project {\n  id: string\n  name: string\n  description?: string\n  webhook_url?: string\n  is_published: boolean\n  public_url?: string\n  components: UIComponent[]\n}\n\ninterface UIBuilderState {\n  currentProject: Project | null\n  selectedComponent: string | null\n  draggedComponent: UIComponent | null\n  isPreviewMode: boolean\n  isSaving: boolean\n  isLoading: boolean\n  error: string | null\n  autoSave: boolean\n  lastSaved: string | null\n  history: Command[]\n  historyIndex: number\n  maxHistorySize: number\n  setCurrentProject: (project: Project | null) => void\n  setSelectedComponent: (componentId: string | null) => void\n  setDraggedComponent: (component: UIComponent | null) => void\n  togglePreviewMode: () => void\n  addComponent: (component: UIComponent) => void\n  updateComponent: (componentId: string, updates: Partial<UIComponent>) => void\n  removeComponent: (componentId: string) => void\n  saveProject: () => Promise<void>\n  loadProject: (projectId: string) => Promise<void>\n  createProject: (name: string, description?: string) => Promise<void>\n  setError: (error: string | null) => void\n  setLoading: (loading: boolean) => void\n  setSaving: (saving: boolean) => void\n  setAutoSave: (autoSave: boolean) => void\n  triggerAutoSave: () => void\n  executeCommand: (command: Command) => void\n  undo: () => void\n  redo: () => void\n  canUndo: () => boolean\n  canRedo: () => boolean\n  clearHistory: () => void\n}\n\nexport const useUIBuilderStore = create<UIBuilderState>()(\n  devtools(\n    (set, get) => ({\n      currentProject: null,\n      selectedComponent: null,\n      draggedComponent: null,\n      isPreviewMode: false,\n      isSaving: false,\n      isLoading: false,\n      error: null,\n      autoSave: true,\n      lastSaved: null,\n      history: [],\n      historyIndex: -1,\n      maxHistorySize: 50,\n      setCurrentProject: (project) => set({ currentProject: project }),\n      setSelectedComponent: (componentId) => set({ selectedComponent: componentId }),\n      setDraggedComponent: (component) => set({ draggedComponent: component }),\n      togglePreviewMode: () => set((state) => ({ isPreviewMode: !state.isPreviewMode })),\n      setError: (error) => set({ error }),\n      setLoading: (isLoading) => set({ isLoading }),\n      setSaving: (isSaving) => set({ isSaving }),\n      setAutoSave: (autoSave) => set({ autoSave }),\n\n      triggerAutoSave: () => {\n        const state = get()\n        if (state.autoSave && state.currentProject && state.currentProject.id !== 'temp-project') {\n          // Debounce auto-save to avoid too many requests\n          setTimeout(() => {\n            const currentState = get()\n            if (currentState.currentProject && !currentState.isSaving) {\n              currentState.saveProject()\n            }\n          }, 1000)\n        }\n      },\n\n      // Command pattern implementation\n      executeCommand: (command: Command) => {\n        const state = get()\n\n        // Execute the command\n        command.execute()\n\n        // Add to history\n        const newHistory = state.history.slice(0, state.historyIndex + 1)\n        newHistory.push(command)\n\n        // Limit history size\n        if (newHistory.length > state.maxHistorySize) {\n          newHistory.shift()\n        }\n\n        set({\n          history: newHistory,\n          historyIndex: newHistory.length - 1\n        })\n      },\n\n      undo: () => {\n        const state = get()\n        if (state.historyIndex >= 0) {\n          const command = state.history[state.historyIndex]\n          command.undo()\n          set({ historyIndex: state.historyIndex - 1 })\n        }\n      },\n\n      redo: () => {\n        const state = get()\n        if (state.historyIndex < state.history.length - 1) {\n          const command = state.history[state.historyIndex + 1]\n          command.execute()\n          set({ historyIndex: state.historyIndex + 1 })\n        }\n      },\n\n      canUndo: () => {\n        const state = get()\n        return state.historyIndex >= 0\n      },\n\n      canRedo: () => {\n        const state = get()\n        return state.historyIndex < state.history.length - 1\n      },\n\n      clearHistory: () => {\n        set({ history: [], historyIndex: -1 })\n      },\n      addComponent: (component) => {\n        set((state) => {\n          if (!state.currentProject) return state\n          return {\n            currentProject: {\n              ...state.currentProject,\n              components: [...state.currentProject.components, component],\n            },\n          }\n        })\n        // Trigger auto-save after adding component\n        get().triggerAutoSave()\n      },\n\n      // Command-based operations for undo/redo\n      addComponentWithHistory: (component) => {\n        const command = new AddComponentCommand(component, { getState: get })\n        get().executeCommand(command)\n        get().triggerAutoSave()\n      },\n\n      removeComponentWithHistory: (componentId) => {\n        const state = get()\n        const component = state.currentProject?.components.find(c => c.id === componentId)\n        if (component) {\n          const command = new RemoveComponentCommand(component, { getState: get })\n          get().executeCommand(command)\n          get().triggerAutoSave()\n        }\n      },\n\n      updateComponentWithHistory: (componentId, updates) => {\n        const state = get()\n        const component = state.currentProject?.components.find(c => c.id === componentId)\n        if (component) {\n          const oldProps = { ...component }\n          const newProps = { ...component, ...updates }\n          const command = new UpdateComponentCommand(componentId, oldProps, newProps, { getState: get })\n          get().executeCommand(command)\n          get().triggerAutoSave()\n        }\n      },\n      updateComponent: (componentId, updates) => {\n        set((state) => {\n          if (!state.currentProject) return state\n          return {\n            currentProject: {\n              ...state.currentProject,\n              components: state.currentProject.components.map((comp) =>\n                comp.id === componentId ? { ...comp, ...updates } : comp\n              ),\n            },\n          }\n        })\n        // Trigger auto-save after updating component\n        get().triggerAutoSave()\n      },\n      removeComponent: (componentId) => {\n        set((state) => {\n          if (!state.currentProject) return state\n          return {\n            currentProject: {\n              ...state.currentProject,\n              components: state.currentProject.components.filter((comp) => comp.id !== componentId),\n            },\n          }\n        })\n        // Trigger auto-save after removing component\n        get().triggerAutoSave()\n      },\n\n      // Project management functions\n      saveProject: async () => {\n        const state = get()\n        if (!state.currentProject) return\n\n        set({ isSaving: true, error: null })\n\n        try {\n          const response = await fetch(`/api/projects/${state.currentProject.id}`, {\n            method: 'PUT',\n            headers: {\n              'Content-Type': 'application/json',\n            },\n            body: JSON.stringify({\n              name: state.currentProject.name,\n              description: state.currentProject.description,\n              webhook_url: state.currentProject.webhook_url,\n              components: state.currentProject.components,\n            }),\n          })\n\n          if (!response.ok) {\n            const errorData = await response.json()\n            throw new Error(errorData.error || 'Failed to save project')\n          }\n\n          const { project } = await response.json()\n          set({\n            currentProject: project,\n            isSaving: false,\n            lastSaved: new Date().toISOString()\n          })\n\n          // Show success toast\n          if (typeof window !== 'undefined' && (window as any).addToast) {\n            ;(window as any).addToast({\n              type: 'success',\n              title: 'Project saved',\n              description: 'Your changes have been saved successfully'\n            })\n          }\n        } catch (error) {\n          console.error('Error saving project:', error)\n          const errorMessage = error instanceof Error ? error.message : 'Failed to save project'\n          set({\n            error: errorMessage,\n            isSaving: false\n          })\n\n          // Show error toast\n          if (typeof window !== 'undefined' && (window as any).addToast) {\n            ;(window as any).addToast({\n              type: 'error',\n              title: 'Save failed',\n              description: errorMessage\n            })\n          }\n        }\n      },\n\n      loadProject: async (projectId: string) => {\n        set({ isLoading: true, error: null })\n\n        try {\n          const response = await fetch(`/api/projects/${projectId}`)\n\n          if (!response.ok) {\n            const errorData = await response.json()\n            throw new Error(errorData.error || 'Failed to load project')\n          }\n\n          const { project } = await response.json()\n          set({ currentProject: project, isLoading: false })\n        } catch (error) {\n          console.error('Error loading project:', error)\n          set({\n            error: error instanceof Error ? error.message : 'Failed to load project',\n            isLoading: false\n          })\n        }\n      },\n\n      createProject: async (name: string, description?: string) => {\n        set({ isSaving: true, error: null })\n\n        try {\n          const response = await fetch('/api/projects', {\n            method: 'POST',\n            headers: {\n              'Content-Type': 'application/json',\n            },\n            body: JSON.stringify({\n              name,\n              description,\n              components: [],\n            }),\n          })\n\n          if (!response.ok) {\n            const errorData = await response.json()\n            throw new Error(errorData.error || 'Failed to create project')\n          }\n\n          const { project } = await response.json()\n          set({ currentProject: project, isSaving: false })\n        } catch (error) {\n          console.error('Error creating project:', error)\n          set({\n            error: error instanceof Error ? error.message : 'Failed to create project',\n            isSaving: false\n          })\n        }\n      },\n    }),\n    { name: 'ui-builder-store' }\n  )\n)\n\n// API Testing Store\ninterface APIRequest {\n  id: string\n  name: string\n  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'\n  url: string\n  headers: Record<string, string>\n  body?: string\n}\n\ninterface APITestingState {\n  requests: APIRequest[]\n  currentRequest: APIRequest | null\n  isLoading: boolean\n  lastResponse: any\n  addRequest: (request: APIRequest) => void\n  updateRequest: (requestId: string, updates: Partial<APIRequest>) => void\n  removeRequest: (requestId: string) => void\n  setCurrentRequest: (request: APIRequest | null) => void\n  setLoading: (loading: boolean) => void\n  setLastResponse: (response: any) => void\n}\n\nexport const useAPITestingStore = create<APITestingState>()(\n  devtools(\n    persist(\n      (set) => ({\n        requests: [],\n        currentRequest: null,\n        isLoading: false,\n        lastResponse: null,\n        addRequest: (request) =>\n          set((state) => ({ requests: [...state.requests, request] })),\n        updateRequest: (requestId, updates) =>\n          set((state) => ({\n            requests: state.requests.map((req) =>\n              req.id === requestId ? { ...req, ...updates } : req\n            ),\n          })),\n        removeRequest: (requestId) =>\n          set((state) => ({\n            requests: state.requests.filter((req) => req.id !== requestId),\n          })),\n        setCurrentRequest: (request) => set({ currentRequest: request }),\n        setLoading: (isLoading) => set({ isLoading }),\n        setLastResponse: (lastResponse) => set({ lastResponse }),\n      }),\n      {\n        name: 'api-testing-storage',\n        partialize: (state) => ({ requests: state.requests }),\n      }\n    ),\n    { name: 'api-testing-store' }\n  )\n)\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;;;;AAUA,0BAA0B;AAC1B,MAAM;IAMJ,UAAU;QACR,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,YAAY,CAAC,IAAI,CAAC,SAAS;IACnD;IAEA,OAAO;QACL,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,eAAe,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;IACzD;IAEA,IAAI,cAAc;QAChB,OAAO,AAAC,OAA0B,OAApB,IAAI,CAAC,SAAS,CAAC,IAAI,EAAC;IACpC;IAfA,YACE,AAAQ,SAAsB,EAC9B,AAAQ,KAAU,CAClB;;;aAFQ,YAAA;aACA,QAAA;IACP;AAaL;AAEA,MAAM;IAMJ,UAAU;QACR,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,eAAe,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;IACzD;IAEA,OAAO;QACL,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,YAAY,CAAC,IAAI,CAAC,SAAS;IACnD;IAEA,IAAI,cAAc;QAChB,OAAO,AAAC,UAA6B,OAApB,IAAI,CAAC,SAAS,CAAC,IAAI,EAAC;IACvC;IAfA,YACE,AAAQ,SAAsB,EAC9B,AAAQ,KAAU,CAClB;;;aAFQ,YAAA;aACA,QAAA;IACP;AAaL;AAEA,MAAM;IAQJ,UAAU;QACR,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,eAAe,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,QAAQ;IACvE;IAEA,OAAO;QACL,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,eAAe,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,QAAQ;IACvE;IAEA,IAAI,cAAc;QAChB,OAAO,AAAC,oBAAoC,OAAjB,IAAI,CAAC,WAAW;IAC7C;IAjBA,YACE,AAAQ,WAAmB,EAC3B,AAAQ,QAA8B,EACtC,AAAQ,QAA8B,EACtC,AAAQ,KAAU,CAClB;;;;;aAJQ,cAAA;aACA,WAAA;aACA,WAAA;aACA,QAAA;IACP;AAaL;AAiBO,MAAM,eAAe,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,IAC/B,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD,EACL,CAAC,MAAQ,CAAC;QACR,MAAM;QACN,WAAW;QACX,SAAS,CAAC,OAAS,IAAI;gBAAE;YAAK;QAC9B,YAAY,CAAC,YAAc,IAAI;gBAAE;YAAU;QAC3C,QAAQ,IAAM,IAAI;gBAAE,MAAM;YAAK;IACjC,CAAC,GACD;IAAE,MAAM;AAAa;AA6DlB,MAAM,oBAAoB,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,IACpC,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD,EACL,CAAC,KAAK,MAAQ,CAAC;QACb,gBAAgB;QAChB,mBAAmB;QACnB,kBAAkB;QAClB,eAAe;QACf,UAAU;QACV,WAAW;QACX,OAAO;QACP,UAAU;QACV,WAAW;QACX,SAAS,EAAE;QACX,cAAc,CAAC;QACf,gBAAgB;QAChB,mBAAmB,CAAC,UAAY,IAAI;gBAAE,gBAAgB;YAAQ;QAC9D,sBAAsB,CAAC,cAAgB,IAAI;gBAAE,mBAAmB;YAAY;QAC5E,qBAAqB,CAAC,YAAc,IAAI;gBAAE,kBAAkB;YAAU;QACtE,mBAAmB,IAAM,IAAI,CAAC,QAAU,CAAC;oBAAE,eAAe,CAAC,MAAM,aAAa;gBAAC,CAAC;QAChF,UAAU,CAAC,QAAU,IAAI;gBAAE;YAAM;QACjC,YAAY,CAAC,YAAc,IAAI;gBAAE;YAAU;QAC3C,WAAW,CAAC,WAAa,IAAI;gBAAE;YAAS;QACxC,aAAa,CAAC,WAAa,IAAI;gBAAE;YAAS;QAE1C,iBAAiB;YACf,MAAM,QAAQ;YACd,IAAI,MAAM,QAAQ,IAAI,MAAM,cAAc,IAAI,MAAM,cAAc,CAAC,EAAE,KAAK,gBAAgB;gBACxF,gDAAgD;gBAChD,WAAW;oBACT,MAAM,eAAe;oBACrB,IAAI,aAAa,cAAc,IAAI,CAAC,aAAa,QAAQ,EAAE;wBACzD,aAAa,WAAW;oBAC1B;gBACF,GAAG;YACL;QACF;QAEA,iCAAiC;QACjC,gBAAgB,CAAC;YACf,MAAM,QAAQ;YAEd,sBAAsB;YACtB,QAAQ,OAAO;YAEf,iBAAiB;YACjB,MAAM,aAAa,MAAM,OAAO,CAAC,KAAK,CAAC,GAAG,MAAM,YAAY,GAAG;YAC/D,WAAW,IAAI,CAAC;YAEhB,qBAAqB;YACrB,IAAI,WAAW,MAAM,GAAG,MAAM,cAAc,EAAE;gBAC5C,WAAW,KAAK;YAClB;YAEA,IAAI;gBACF,SAAS;gBACT,cAAc,WAAW,MAAM,GAAG;YACpC;QACF;QAEA,MAAM;YACJ,MAAM,QAAQ;YACd,IAAI,MAAM,YAAY,IAAI,GAAG;gBAC3B,MAAM,UAAU,MAAM,OAAO,CAAC,MAAM,YAAY,CAAC;gBACjD,QAAQ,IAAI;gBACZ,IAAI;oBAAE,cAAc,MAAM,YAAY,GAAG;gBAAE;YAC7C;QACF;QAEA,MAAM;YACJ,MAAM,QAAQ;YACd,IAAI,MAAM,YAAY,GAAG,MAAM,OAAO,CAAC,MAAM,GAAG,GAAG;gBACjD,MAAM,UAAU,MAAM,OAAO,CAAC,MAAM,YAAY,GAAG,EAAE;gBACrD,QAAQ,OAAO;gBACf,IAAI;oBAAE,cAAc,MAAM,YAAY,GAAG;gBAAE;YAC7C;QACF;QAEA,SAAS;YACP,MAAM,QAAQ;YACd,OAAO,MAAM,YAAY,IAAI;QAC/B;QAEA,SAAS;YACP,MAAM,QAAQ;YACd,OAAO,MAAM,YAAY,GAAG,MAAM,OAAO,CAAC,MAAM,GAAG;QACrD;QAEA,cAAc;YACZ,IAAI;gBAAE,SAAS,EAAE;gBAAE,cAAc,CAAC;YAAE;QACtC;QACA,cAAc,CAAC;YACb,IAAI,CAAC;gBACH,IAAI,CAAC,MAAM,cAAc,EAAE,OAAO;gBAClC,OAAO;oBACL,gBAAgB;wBACd,GAAG,MAAM,cAAc;wBACvB,YAAY;+BAAI,MAAM,cAAc,CAAC,UAAU;4BAAE;yBAAU;oBAC7D;gBACF;YACF;YACA,2CAA2C;YAC3C,MAAM,eAAe;QACvB;QAEA,yCAAyC;QACzC,yBAAyB,CAAC;YACxB,MAAM,UAAU,IAAI,oBAAoB,WAAW;gBAAE,UAAU;YAAI;YACnE,MAAM,cAAc,CAAC;YACrB,MAAM,eAAe;QACvB;QAEA,4BAA4B,CAAC;gBAET;YADlB,MAAM,QAAQ;YACd,MAAM,aAAY,wBAAA,MAAM,cAAc,cAApB,4CAAA,sBAAsB,UAAU,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;YACtE,IAAI,WAAW;gBACb,MAAM,UAAU,IAAI,uBAAuB,WAAW;oBAAE,UAAU;gBAAI;gBACtE,MAAM,cAAc,CAAC;gBACrB,MAAM,eAAe;YACvB;QACF;QAEA,4BAA4B,CAAC,aAAa;gBAEtB;YADlB,MAAM,QAAQ;YACd,MAAM,aAAY,wBAAA,MAAM,cAAc,cAApB,4CAAA,sBAAsB,UAAU,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;YACtE,IAAI,WAAW;gBACb,MAAM,WAAW;oBAAE,GAAG,SAAS;gBAAC;gBAChC,MAAM,WAAW;oBAAE,GAAG,SAAS;oBAAE,GAAG,OAAO;gBAAC;gBAC5C,MAAM,UAAU,IAAI,uBAAuB,aAAa,UAAU,UAAU;oBAAE,UAAU;gBAAI;gBAC5F,MAAM,cAAc,CAAC;gBACrB,MAAM,eAAe;YACvB;QACF;QACA,iBAAiB,CAAC,aAAa;YAC7B,IAAI,CAAC;gBACH,IAAI,CAAC,MAAM,cAAc,EAAE,OAAO;gBAClC,OAAO;oBACL,gBAAgB;wBACd,GAAG,MAAM,cAAc;wBACvB,YAAY,MAAM,cAAc,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,OAC/C,KAAK,EAAE,KAAK,cAAc;gCAAE,GAAG,IAAI;gCAAE,GAAG,OAAO;4BAAC,IAAI;oBAExD;gBACF;YACF;YACA,6CAA6C;YAC7C,MAAM,eAAe;QACvB;QACA,iBAAiB,CAAC;YAChB,IAAI,CAAC;gBACH,IAAI,CAAC,MAAM,cAAc,EAAE,OAAO;gBAClC,OAAO;oBACL,gBAAgB;wBACd,GAAG,MAAM,cAAc;wBACvB,YAAY,MAAM,cAAc,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,OAAS,KAAK,EAAE,KAAK;oBAC3E;gBACF;YACF;YACA,6CAA6C;YAC7C,MAAM,eAAe;QACvB;QAEA,+BAA+B;QAC/B,aAAa;YACX,MAAM,QAAQ;YACd,IAAI,CAAC,MAAM,cAAc,EAAE;YAE3B,IAAI;gBAAE,UAAU;gBAAM,OAAO;YAAK;YAElC,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM,AAAC,iBAAwC,OAAxB,MAAM,cAAc,CAAC,EAAE,GAAI;oBACvE,QAAQ;oBACR,SAAS;wBACP,gBAAgB;oBAClB;oBACA,MAAM,KAAK,SAAS,CAAC;wBACnB,MAAM,MAAM,cAAc,CAAC,IAAI;wBAC/B,aAAa,MAAM,cAAc,CAAC,WAAW;wBAC7C,aAAa,MAAM,cAAc,CAAC,WAAW;wBAC7C,YAAY,MAAM,cAAc,CAAC,UAAU;oBAC7C;gBACF;gBAEA,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;oBACrC,MAAM,IAAI,MAAM,UAAU,KAAK,IAAI;gBACrC;gBAEA,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM,SAAS,IAAI;gBACvC,IAAI;oBACF,gBAAgB;oBAChB,UAAU;oBACV,WAAW,IAAI,OAAO,WAAW;gBACnC;gBAEA,qBAAqB;gBACrB,IAAI,aAAkB,eAAe,AAAC,OAAe,QAAQ,EAAE;;oBAC3D,OAAe,QAAQ,CAAC;wBACxB,MAAM;wBACN,OAAO;wBACP,aAAa;oBACf;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,yBAAyB;gBACvC,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D,IAAI;oBACF,OAAO;oBACP,UAAU;gBACZ;gBAEA,mBAAmB;gBACnB,IAAI,aAAkB,eAAe,AAAC,OAAe,QAAQ,EAAE;;oBAC3D,OAAe,QAAQ,CAAC;wBACxB,MAAM;wBACN,OAAO;wBACP,aAAa;oBACf;gBACF;YACF;QACF;QAEA,aAAa,OAAO;YAClB,IAAI;gBAAE,WAAW;gBAAM,OAAO;YAAK;YAEnC,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM,AAAC,iBAA0B,OAAV;gBAE9C,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;oBACrC,MAAM,IAAI,MAAM,UAAU,KAAK,IAAI;gBACrC;gBAEA,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM,SAAS,IAAI;gBACvC,IAAI;oBAAE,gBAAgB;oBAAS,WAAW;gBAAM;YAClD,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,0BAA0B;gBACxC,IAAI;oBACF,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;oBAChD,WAAW;gBACb;YACF;QACF;QAEA,eAAe,OAAO,MAAc;YAClC,IAAI;gBAAE,UAAU;gBAAM,OAAO;YAAK;YAElC,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM,iBAAiB;oBAC5C,QAAQ;oBACR,SAAS;wBACP,gBAAgB;oBAClB;oBACA,MAAM,KAAK,SAAS,CAAC;wBACnB;wBACA;wBACA,YAAY,EAAE;oBAChB;gBACF;gBAEA,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;oBACrC,MAAM,IAAI,MAAM,UAAU,KAAK,IAAI;gBACrC;gBAEA,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM,SAAS,IAAI;gBACvC,IAAI;oBAAE,gBAAgB;oBAAS,UAAU;gBAAM;YACjD,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,2BAA2B;gBACzC,IAAI;oBACF,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;oBAChD,UAAU;gBACZ;YACF;QACF;IACF,CAAC,GACD;IAAE,MAAM;AAAmB;AA2BxB,MAAM,qBAAqB,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,IACrC,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD,EACL,CAAA,GAAA,gJAAA,CAAA,UAAO,AAAD,EACJ,CAAC,MAAQ,CAAC;QACR,UAAU,EAAE;QACZ,gBAAgB;QAChB,WAAW;QACX,cAAc;QACd,YAAY,CAAC,UACX,IAAI,CAAC,QAAU,CAAC;oBAAE,UAAU;2BAAI,MAAM,QAAQ;wBAAE;qBAAQ;gBAAC,CAAC;QAC5D,eAAe,CAAC,WAAW,UACzB,IAAI,CAAC,QAAU,CAAC;oBACd,UAAU,MAAM,QAAQ,CAAC,GAAG,CAAC,CAAC,MAC5B,IAAI,EAAE,KAAK,YAAY;4BAAE,GAAG,GAAG;4BAAE,GAAG,OAAO;wBAAC,IAAI;gBAEpD,CAAC;QACH,eAAe,CAAC,YACd,IAAI,CAAC,QAAU,CAAC;oBACd,UAAU,MAAM,QAAQ,CAAC,MAAM,CAAC,CAAC,MAAQ,IAAI,EAAE,KAAK;gBACtD,CAAC;QACH,mBAAmB,CAAC,UAAY,IAAI;gBAAE,gBAAgB;YAAQ;QAC9D,YAAY,CAAC,YAAc,IAAI;gBAAE;YAAU;QAC3C,iBAAiB,CAAC,eAAiB,IAAI;gBAAE;YAAa;IACxD,CAAC,GACD;IACE,MAAM;IACN,YAAY,CAAC,QAAU,CAAC;YAAE,UAAU,MAAM,QAAQ;QAAC,CAAC;AACtD,IAEF;IAAE,MAAM;AAAoB", "debugId": null}}, {"offset": {"line": 463, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/FlowUi/src/hooks/useAuth.ts"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { createClientComponentClient } from '@/lib/supabase'\nimport { useAuthStore } from '@/lib/store'\nimport { User } from '@/types'\n\nexport function useAuth() {\n  const router = useRouter()\n  const supabase = createClientComponentClient()\n  const { user, isLoading, setUser, setLoading } = useAuthStore()\n  const [isHydrated, setIsHydrated] = useState(false)\n\n  useEffect(() => {\n    // Set hydrated flag\n    setIsHydrated(true)\n\n    // Get initial session\n    const getInitialSession = async () => {\n      try {\n        console.log('🔍 Getting initial session...')\n        const { data: { session }, error } = await supabase.auth.getSession()\n\n        if (error) {\n          console.error('❌ Error getting session:', error)\n          setUser(null)\n        } else if (session?.user) {\n          console.log('✅ Session found, setting user:', session.user.email)\n          // For now, create user from session data (database setup not required)\n          setUser({\n            id: session.user.id,\n            email: session.user.email!,\n            subscription_tier: 'free',\n            created_at: session.user.created_at || new Date().toISOString(),\n            updated_at: new Date().toISOString(),\n          })\n        } else {\n          console.log('ℹ️ No session found')\n          setUser(null)\n        }\n      } catch (error) {\n        console.error('❌ Error in getInitialSession:', error)\n        setUser(null)\n      } finally {\n        console.log('🏁 Setting loading to false')\n        setLoading(false)\n      }\n    }\n\n    getInitialSession()\n\n    // Listen for auth changes\n    const { data: { subscription } } = supabase.auth.onAuthStateChange(\n      async (event, session) => {\n        if (event === 'SIGNED_IN' && session?.user) {\n          // Handle sign in - create user from session data\n          setUser({\n            id: session.user.id,\n            email: session.user.email!,\n            subscription_tier: 'free',\n            created_at: session.user.created_at || new Date().toISOString(),\n            updated_at: new Date().toISOString(),\n          })\n          router.push('/dashboard')\n        } else if (event === 'SIGNED_OUT') {\n          // Handle sign out\n          setUser(null)\n          router.push('/')\n        }\n      }\n    )\n\n    return () => {\n      subscription.unsubscribe()\n    }\n  }, [supabase, setUser, setLoading, router])\n\n  const signIn = async (email: string, password: string) => {\n    try {\n      setLoading(true)\n      const { error } = await supabase.auth.signInWithPassword({\n        email,\n        password,\n      })\n\n      if (error) throw error\n      \n      return { success: true }\n    } catch (error: any) {\n      return { \n        success: false, \n        error: error.message || 'An error occurred during sign in' \n      }\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const signUp = async (email: string, password: string) => {\n    try {\n      setLoading(true)\n      const { error } = await supabase.auth.signUp({\n        email,\n        password,\n        options: {\n          emailRedirectTo: `${window.location.origin}/auth/callback`,\n        },\n      })\n\n      if (error) throw error\n      \n      return { success: true }\n    } catch (error: any) {\n      return { \n        success: false, \n        error: error.message || 'An error occurred during sign up' \n      }\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const signOut = async () => {\n    try {\n      setLoading(true)\n      const { error } = await supabase.auth.signOut()\n      if (error) throw error\n      \n      return { success: true }\n    } catch (error: any) {\n      return { \n        success: false, \n        error: error.message || 'An error occurred during sign out' \n      }\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const signInWithProvider = async (provider: 'google' | 'github') => {\n    try {\n      setLoading(true)\n      const { error } = await supabase.auth.signInWithOAuth({\n        provider,\n        options: {\n          redirectTo: `${window.location.origin}/auth/callback`,\n        },\n      })\n\n      if (error) throw error\n      \n      return { success: true }\n    } catch (error: any) {\n      return { \n        success: false, \n        error: error.message || 'An error occurred during OAuth sign in' \n      }\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const resetPassword = async (email: string) => {\n    try {\n      setLoading(true)\n      const { error } = await supabase.auth.resetPasswordForEmail(email, {\n        redirectTo: `${window.location.origin}/auth/reset-password`,\n      })\n\n      if (error) throw error\n      \n      return { success: true }\n    } catch (error: any) {\n      return { \n        success: false, \n        error: error.message || 'An error occurred during password reset' \n      }\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  return {\n    user,\n    isLoading: isLoading || !isHydrated,\n    signIn,\n    signUp,\n    signOut,\n    signInWithProvider,\n    resetPassword,\n    isAuthenticated: isHydrated && !!user,\n  }\n}\n"], "names": [], "mappings": ";;;AAEA;AACA;AACA;AACA;;AALA;;;;;AAQO,SAAS;;IACd,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,yHAAA,CAAA,8BAA2B,AAAD;IAC3C,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD;IAC5D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6BAAE;YACR,oBAAoB;YACpB,cAAc;YAEd,sBAAsB;YACtB,MAAM;uDAAoB;oBACxB,IAAI;wBACF,QAAQ,GAAG,CAAC;wBACZ,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU;wBAEnE,IAAI,OAAO;4BACT,QAAQ,KAAK,CAAC,4BAA4B;4BAC1C,QAAQ;wBACV,OAAO,IAAI,oBAAA,8BAAA,QAAS,IAAI,EAAE;4BACxB,QAAQ,GAAG,CAAC,kCAAkC,QAAQ,IAAI,CAAC,KAAK;4BAChE,uEAAuE;4BACvE,QAAQ;gCACN,IAAI,QAAQ,IAAI,CAAC,EAAE;gCACnB,OAAO,QAAQ,IAAI,CAAC,KAAK;gCACzB,mBAAmB;gCACnB,YAAY,QAAQ,IAAI,CAAC,UAAU,IAAI,IAAI,OAAO,WAAW;gCAC7D,YAAY,IAAI,OAAO,WAAW;4BACpC;wBACF,OAAO;4BACL,QAAQ,GAAG,CAAC;4BACZ,QAAQ;wBACV;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,iCAAiC;wBAC/C,QAAQ;oBACV,SAAU;wBACR,QAAQ,GAAG,CAAC;wBACZ,WAAW;oBACb;gBACF;;YAEA;YAEA,0BAA0B;YAC1B,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,EAAE,GAAG,SAAS,IAAI,CAAC,iBAAiB;qCAChE,OAAO,OAAO;oBACZ,IAAI,UAAU,gBAAe,oBAAA,8BAAA,QAAS,IAAI,GAAE;wBAC1C,iDAAiD;wBACjD,QAAQ;4BACN,IAAI,QAAQ,IAAI,CAAC,EAAE;4BACnB,OAAO,QAAQ,IAAI,CAAC,KAAK;4BACzB,mBAAmB;4BACnB,YAAY,QAAQ,IAAI,CAAC,UAAU,IAAI,IAAI,OAAO,WAAW;4BAC7D,YAAY,IAAI,OAAO,WAAW;wBACpC;wBACA,OAAO,IAAI,CAAC;oBACd,OAAO,IAAI,UAAU,cAAc;wBACjC,kBAAkB;wBAClB,QAAQ;wBACR,OAAO,IAAI,CAAC;oBACd;gBACF;;YAGF;qCAAO;oBACL,aAAa,WAAW;gBAC1B;;QACF;4BAAG;QAAC;QAAU;QAAS;QAAY;KAAO;IAE1C,MAAM,SAAS,OAAO,OAAe;QACnC,IAAI;YACF,WAAW;YACX,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,kBAAkB,CAAC;gBACvD;gBACA;YACF;YAEA,IAAI,OAAO,MAAM;YAEjB,OAAO;gBAAE,SAAS;YAAK;QACzB,EAAE,OAAO,OAAY;YACnB,OAAO;gBACL,SAAS;gBACT,OAAO,MAAM,OAAO,IAAI;YAC1B;QACF,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,SAAS,OAAO,OAAe;QACnC,IAAI;YACF,WAAW;YACX,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,MAAM,CAAC;gBAC3C;gBACA;gBACA,SAAS;oBACP,iBAAiB,AAAC,GAAyB,OAAvB,OAAO,QAAQ,CAAC,MAAM,EAAC;gBAC7C;YACF;YAEA,IAAI,OAAO,MAAM;YAEjB,OAAO;gBAAE,SAAS;YAAK;QACzB,EAAE,OAAO,OAAY;YACnB,OAAO;gBACL,SAAS;gBACT,OAAO,MAAM,OAAO,IAAI;YAC1B;QACF,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,UAAU;QACd,IAAI;YACF,WAAW;YACX,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;YAC7C,IAAI,OAAO,MAAM;YAEjB,OAAO;gBAAE,SAAS;YAAK;QACzB,EAAE,OAAO,OAAY;YACnB,OAAO;gBACL,SAAS;gBACT,OAAO,MAAM,OAAO,IAAI;YAC1B;QACF,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,qBAAqB,OAAO;QAChC,IAAI;YACF,WAAW;YACX,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,eAAe,CAAC;gBACpD;gBACA,SAAS;oBACP,YAAY,AAAC,GAAyB,OAAvB,OAAO,QAAQ,CAAC,MAAM,EAAC;gBACxC;YACF;YAEA,IAAI,OAAO,MAAM;YAEjB,OAAO;gBAAE,SAAS;YAAK;QACzB,EAAE,OAAO,OAAY;YACnB,OAAO;gBACL,SAAS;gBACT,OAAO,MAAM,OAAO,IAAI;YAC1B;QACF,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,gBAAgB,OAAO;QAC3B,IAAI;YACF,WAAW;YACX,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,qBAAqB,CAAC,OAAO;gBACjE,YAAY,AAAC,GAAyB,OAAvB,OAAO,QAAQ,CAAC,MAAM,EAAC;YACxC;YAEA,IAAI,OAAO,MAAM;YAEjB,OAAO;gBAAE,SAAS;YAAK;QACzB,EAAE,OAAO,OAAY;YACnB,OAAO;gBACL,SAAS;gBACT,OAAO,MAAM,OAAO,IAAI;YAC1B;QACF,SAAU;YACR,WAAW;QACb;IACF;IAEA,OAAO;QACL;QACA,WAAW,aAAa,CAAC;QACzB;QACA;QACA;QACA;QACA;QACA,iBAAiB,cAAc,CAAC,CAAC;IACnC;AACF;GAzLgB;;QACC,qIAAA,CAAA,YAAS;QAEyB,sHAAA,CAAA,eAAY", "debugId": null}}, {"offset": {"line": 678, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/FlowUi/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function generateId(): string {\n  return Math.random().toString(36).substr(2, 9)\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAC9C", "debugId": null}}, {"offset": {"line": 704, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/FlowUi/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,KASb;QATa,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF,GATa;IAUd,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 768, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/FlowUi/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACZ,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACjB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACvB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACnB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 890, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/FlowUi/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,KAM6C;QAN7C,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD,GAN7C;IAOb,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAhBS", "debugId": null}}, {"offset": {"line": 943, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/FlowUi/src/components/loading.tsx"], "sourcesContent": ["interface LoadingProps {\n  message?: string\n}\n\nexport function Loading({ message = \"Loading...\" }: LoadingProps) {\n  return (\n    <div className=\"min-h-screen flex items-center justify-center\">\n      <div className=\"text-center\">\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4\"></div>\n        <p className=\"text-muted-foreground\">{message}</p>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAIO,SAAS,QAAQ,KAAwC;QAAxC,EAAE,UAAU,YAAY,EAAgB,GAAxC;IACtB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;;;;;8BACf,6LAAC;oBAAE,WAAU;8BAAyB;;;;;;;;;;;;;;;;;AAI9C;KATgB", "debugId": null}}, {"offset": {"line": 994, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/FlowUi/src/app/dashboard/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useAuth } from '@/hooks/useAuth'\nimport { Button } from '@/components/ui/button'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport { Loading } from '@/components/loading'\nimport { useRouter } from 'next/navigation'\nimport { useEffect } from 'react'\n\nexport default function DashboardPage() {\n  const { user, isLoading, signOut, isAuthenticated } = useAuth()\n  const router = useRouter()\n\n  useEffect(() => {\n    console.log('🎯 Dashboard state:', { isLoading, isAuthenticated, user: user?.email })\n    if (!isLoading && !isAuthenticated) {\n      console.log('🔄 Redirecting to signin...')\n      router.push('/auth/signin')\n    }\n  }, [isLoading, isAuthenticated, router, user])\n\n  // Temporarily bypass auth check for testing\n  if (isLoading) {\n    console.log('🔄 Still loading...')\n    return <Loading />\n  }\n\n  // For testing, let's show the dashboard even without auth\n  // if (!isAuthenticated) {\n  //   return null // Will redirect to signin\n  // }\n\n  const handleSignOut = async () => {\n    await signOut()\n  }\n\n  const handleNewProject = () => {\n    // Navigate directly to the builder for now\n    router.push('/builder')\n  }\n\n  const handleAPITesting = () => {\n    router.push('/api-testing')\n  }\n\n  const handleSchedules = () => {\n    // For now, show an alert. Later this will navigate to schedules page\n    alert('🚧 Scheduling feature coming soon!\\n\\nThis will open the scheduling system with:\\n• Cron-based scheduling\\n• Manual triggers\\n• Execution history\\n• Failure notifications')\n  }\n\n  const handleCreateFirstProject = () => {\n    router.push('/builder')\n  }\n\n  // For testing, create a fallback user if none exists\n  const displayUser = user || {\n    email: '<EMAIL>',\n    subscription_tier: 'free'\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800\" suppressHydrationWarning>\n        {/* Header */}\n        <header className=\"border-b bg-white/80 backdrop-blur-sm dark:bg-gray-900/80\">\n          <div className=\"container mx-auto px-4 py-4\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"w-8 h-8 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg\"></div>\n                <span className=\"text-xl font-bold\">FlowUI</span>\n              </div>\n              <div className=\"flex items-center space-x-4\">\n                <Badge variant=\"secondary\">{displayUser?.subscription_tier}</Badge>\n                <span className=\"text-sm text-muted-foreground\">{displayUser?.email}</span>\n                <Button variant=\"outline\" onClick={handleSignOut}>\n                  Sign Out\n                </Button>\n              </div>\n            </div>\n          </div>\n        </header>\n\n      {/* Main Content */}\n      <main className=\"container mx-auto px-4 py-8\">\n        <div className=\"mb-8\">\n          <h1 className=\"text-3xl font-bold text-gray-900 dark:text-white mb-2\">\n            Welcome back!\n          </h1>\n          <p className=\"text-gray-600 dark:text-gray-300\">\n            Ready to build some amazing interfaces? Let's get started.\n          </p>\n        </div>\n\n        {/* Quick Actions */}\n        <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8\">\n          <Card className=\"cursor-pointer hover:shadow-lg transition-shadow\">\n            <CardHeader>\n              <CardTitle className=\"flex items-center gap-2\">\n                🎨 Create New Project\n              </CardTitle>\n              <CardDescription>\n                Start building a new UI interface from scratch\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <Button className=\"w-full\" onClick={handleNewProject}>\n                New Project\n              </Button>\n            </CardContent>\n          </Card>\n\n          <Card className=\"cursor-pointer hover:shadow-lg transition-shadow\">\n            <CardHeader>\n              <CardTitle className=\"flex items-center gap-2\">\n                🧪 API Testing\n              </CardTitle>\n              <CardDescription>\n                Test your N8N webhooks and API endpoints\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <Button variant=\"outline\" className=\"w-full\" onClick={handleAPITesting}>\n                Open Tester\n              </Button>\n            </CardContent>\n          </Card>\n\n          <Card className=\"cursor-pointer hover:shadow-lg transition-shadow\">\n            <CardHeader>\n              <CardTitle className=\"flex items-center gap-2\">\n                📅 Schedules\n              </CardTitle>\n              <CardDescription>\n                Manage your automation schedules and triggers\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <Button variant=\"outline\" className=\"w-full\" onClick={handleSchedules}>\n                View Schedules\n              </Button>\n            </CardContent>\n          </Card>\n        </div>\n\n        {/* Recent Projects */}\n        <div className=\"mb-8\">\n          <h2 className=\"text-2xl font-semibold mb-4\">Recent Projects</h2>\n          <Card>\n            <CardContent className=\"p-6\">\n              <div className=\"text-center py-8\">\n                <div className=\"w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mx-auto mb-4\">\n                  <svg className=\"w-8 h-8 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10\" />\n                  </svg>\n                </div>\n                <h3 className=\"text-lg font-medium mb-2\">No projects yet</h3>\n                <p className=\"text-muted-foreground mb-4\">\n                  Create your first project to start building amazing interfaces\n                </p>\n                <Button onClick={handleCreateFirstProject}>\n                  Create Your First Project\n                </Button>\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n\n        {/* Stats */}\n        <div className=\"grid md:grid-cols-4 gap-4\">\n          <Card>\n            <CardContent className=\"p-6\">\n              <div className=\"text-2xl font-bold\">0</div>\n              <p className=\"text-sm text-muted-foreground\">Projects</p>\n            </CardContent>\n          </Card>\n          <Card>\n            <CardContent className=\"p-6\">\n              <div className=\"text-2xl font-bold\">0</div>\n              <p className=\"text-sm text-muted-foreground\">Published Pages</p>\n            </CardContent>\n          </Card>\n          <Card>\n            <CardContent className=\"p-6\">\n              <div className=\"text-2xl font-bold\">0</div>\n              <p className=\"text-sm text-muted-foreground\">API Requests</p>\n            </CardContent>\n          </Card>\n          <Card>\n            <CardContent className=\"p-6\">\n              <div className=\"text-2xl font-bold\">0</div>\n              <p className=\"text-sm text-muted-foreground\">Schedules</p>\n            </CardContent>\n          </Card>\n        </div>\n      </main>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;AARA;;;;;;;;AAUe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,UAAO,AAAD;IAC5D,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,QAAQ,GAAG,CAAC,uBAAuB;gBAAE;gBAAW;gBAAiB,IAAI,EAAE,iBAAA,2BAAA,KAAM,KAAK;YAAC;YACnF,IAAI,CAAC,aAAa,CAAC,iBAAiB;gBAClC,QAAQ,GAAG,CAAC;gBACZ,OAAO,IAAI,CAAC;YACd;QACF;kCAAG;QAAC;QAAW;QAAiB;QAAQ;KAAK;IAE7C,4CAA4C;IAC5C,IAAI,WAAW;QACb,QAAQ,GAAG,CAAC;QACZ,qBAAO,6LAAC,gIAAA,CAAA,UAAO;;;;;IACjB;IAEA,0DAA0D;IAC1D,0BAA0B;IAC1B,2CAA2C;IAC3C,IAAI;IAEJ,MAAM,gBAAgB;QACpB,MAAM;IACR;IAEA,MAAM,mBAAmB;QACvB,2CAA2C;QAC3C,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,mBAAmB;QACvB,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,kBAAkB;QACtB,qEAAqE;QACrE,MAAM;IACR;IAEA,MAAM,2BAA2B;QAC/B,OAAO,IAAI,CAAC;IACd;IAEA,qDAAqD;IACrD,MAAM,cAAc,QAAQ;QAC1B,OAAO;QACP,mBAAmB;IACrB;IAEA,qBACE,6LAAC;QAAI,WAAU;QAAgG,wBAAwB;;0BAEnI,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAK,WAAU;kDAAoB;;;;;;;;;;;;0CAEtC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,oIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAa,wBAAA,kCAAA,YAAa,iBAAiB;;;;;;kDAC1D,6LAAC;wCAAK,WAAU;kDAAiC,wBAAA,kCAAA,YAAa,KAAK;;;;;;kDACnE,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,SAAS;kDAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS5D,6LAAC;gBAAK,WAAU;;kCACd,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAwD;;;;;;0CAGtE,6LAAC;gCAAE,WAAU;0CAAmC;;;;;;;;;;;;kCAMlD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,6LAAC,mIAAA,CAAA,aAAU;;0DACT,6LAAC,mIAAA,CAAA,YAAS;gDAAC,WAAU;0DAA0B;;;;;;0DAG/C,6LAAC,mIAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;kDAInB,6LAAC,mIAAA,CAAA,cAAW;kDACV,cAAA,6LAAC,qIAAA,CAAA,SAAM;4CAAC,WAAU;4CAAS,SAAS;sDAAkB;;;;;;;;;;;;;;;;;0CAM1D,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,6LAAC,mIAAA,CAAA,aAAU;;0DACT,6LAAC,mIAAA,CAAA,YAAS;gDAAC,WAAU;0DAA0B;;;;;;0DAG/C,6LAAC,mIAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;kDAInB,6LAAC,mIAAA,CAAA,cAAW;kDACV,cAAA,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,WAAU;4CAAS,SAAS;sDAAkB;;;;;;;;;;;;;;;;;0CAM5E,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,6LAAC,mIAAA,CAAA,aAAU;;0DACT,6LAAC,mIAAA,CAAA,YAAS;gDAAC,WAAU;0DAA0B;;;;;;0DAG/C,6LAAC,mIAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;kDAInB,6LAAC,mIAAA,CAAA,cAAW;kDACV,cAAA,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,WAAU;4CAAS,SAAS;sDAAiB;;;;;;;;;;;;;;;;;;;;;;;kCAQ7E,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA8B;;;;;;0CAC5C,6LAAC,mIAAA,CAAA,OAAI;0CACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;oDAAwB,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DAC/E,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;0DAGzE,6LAAC;gDAAG,WAAU;0DAA2B;;;;;;0DACzC,6LAAC;gDAAE,WAAU;0DAA6B;;;;;;0DAG1C,6LAAC,qIAAA,CAAA,SAAM;gDAAC,SAAS;0DAA0B;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCASnD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,mIAAA,CAAA,OAAI;0CACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,6LAAC;4CAAI,WAAU;sDAAqB;;;;;;sDACpC,6LAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;;;;;;0CAGjD,6LAAC,mIAAA,CAAA,OAAI;0CACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,6LAAC;4CAAI,WAAU;sDAAqB;;;;;;sDACpC,6LAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;;;;;;0CAGjD,6LAAC,mIAAA,CAAA,OAAI;0CACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,6LAAC;4CAAI,WAAU;sDAAqB;;;;;;sDACpC,6LAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;;;;;;0CAGjD,6LAAC,mIAAA,CAAA,OAAI;0CACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,6LAAC;4CAAI,WAAU;sDAAqB;;;;;;sDACpC,6LAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO3D;GA3LwB;;QACgC,0HAAA,CAAA,UAAO;QAC9C,qIAAA,CAAA,YAAS;;;KAFF", "debugId": null}}]}