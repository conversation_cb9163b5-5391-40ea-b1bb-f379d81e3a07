{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_6813fc78._.js", "server/edge/chunks/[root-of-the-server]__de110ef0._.js", "server/edge/chunks/edge-wrapper_895135be.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "XBnWTXQTpZuckHnYdyfrhWX2ugBIK4p1ITOuQN7l/Wo=", "__NEXT_PREVIEW_MODE_ID": "5ec281c405a92b30beaecdb891ce18a9", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "6f96f807995b5c11ed61a5e9cae7c124502065a9fe0516a26f71d4d2e03eadd8", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "446ab081f22ed266240630a2941ee74a04a4adf35f36f94beb4f164d97aa36c9"}}}, "sortedMiddleware": ["/"], "functions": {}}