{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 154, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/FlowUi/src/lib/supabase-server.ts"], "sourcesContent": ["import { createServerClient } from '@supabase/ssr'\nimport { cookies } from 'next/headers'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n\n// Server-side Supabase client for Server Components\nexport const createServerComponentClient = async () => {\n  const cookieStore = await cookies()\n  \n  return createServerClient(supabaseUrl, supabaseAnonKey, {\n    cookies: {\n      getAll() {\n        return cookieStore.getAll()\n      },\n      setAll(cookiesToSet) {\n        try {\n          cookiesToSet.forEach(({ name, value, options }) =>\n            cookieStore.set(name, value, options)\n          )\n        } catch {\n          // The `setAll` method was called from a Server Component.\n          // This can be ignored if you have middleware refreshing\n          // user sessions.\n        }\n      },\n    },\n  })\n}\n\n// Server-side Supabase client for Route Handlers\nexport const createRouteHandlerClient = (request: Request) => {\n  const response = new Response()\n  \n  return createServerClient(supabaseUrl, supabase<PERSON>non<PERSON>ey, {\n    cookies: {\n      getAll() {\n        const cookieHeader = request.headers.get('cookie')\n        if (!cookieHeader) return []\n        \n        return cookieHeader.split(';').map(cookie => {\n          const [name, value] = cookie.trim().split('=')\n          return { name, value }\n        })\n      },\n      setAll(cookiesToSet) {\n        cookiesToSet.forEach(({ name, value, options }) => {\n          response.headers.append('Set-Cookie', `${name}=${value}; ${Object.entries(options || {}).map(([k, v]) => `${k}=${v}`).join('; ')}`)\n        })\n      },\n    },\n  })\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AACA;;;AAEA,MAAM;AACN,MAAM;AAGC,MAAM,8BAA8B;IACzC,MAAM,cAAc,MAAM,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IAEhC,OAAO,CAAA,GAAA,2KAAA,CAAA,qBAAkB,AAAD,EAAE,aAAa,iBAAiB;QACtD,SAAS;YACP;gBACE,OAAO,YAAY,MAAM;YAC3B;YACA,QAAO,YAAY;gBACjB,IAAI;oBACF,aAAa,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,GAC5C,YAAY,GAAG,CAAC,MAAM,OAAO;gBAEjC,EAAE,OAAM;gBACN,0DAA0D;gBAC1D,wDAAwD;gBACxD,iBAAiB;gBACnB;YACF;QACF;IACF;AACF;AAGO,MAAM,2BAA2B,CAAC;IACvC,MAAM,WAAW,IAAI;IAErB,OAAO,CAAA,GAAA,2KAAA,CAAA,qBAAkB,AAAD,EAAE,aAAa,iBAAiB;QACtD,SAAS;YACP;gBACE,MAAM,eAAe,QAAQ,OAAO,CAAC,GAAG,CAAC;gBACzC,IAAI,CAAC,cAAc,OAAO,EAAE;gBAE5B,OAAO,aAAa,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA;oBACjC,MAAM,CAAC,MAAM,MAAM,GAAG,OAAO,IAAI,GAAG,KAAK,CAAC;oBAC1C,OAAO;wBAAE;wBAAM;oBAAM;gBACvB;YACF;YACA,QAAO,YAAY;gBACjB,aAAa,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE;oBAC5C,SAAS,OAAO,CAAC,MAAM,CAAC,cAAc,GAAG,KAAK,CAAC,EAAE,MAAM,EAAE,EAAE,OAAO,OAAO,CAAC,WAAW,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,GAAK,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,IAAI,CAAC,OAAO;gBACpI;YACF;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 211, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/FlowUi/src/app/api/auth/ensure-user/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { createRouteHandlerClient } from '@/lib/supabase-server'\n\nexport async function POST(request: NextRequest) {\n  try {\n    const supabase = createRouteHandlerClient(request)\n    \n    // Get the current user\n    const { data: { user }, error: authError } = await supabase.auth.getUser()\n    \n    if (authError || !user) {\n      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })\n    }\n\n    // Check if user exists in our users table\n    const { data: existingUser, error: userError } = await supabase\n      .from('users')\n      .select('id')\n      .eq('id', user.id)\n      .single()\n\n    if (userError && userError.code !== 'PGRST116') {\n      console.error('Error checking user:', userError)\n      return NextResponse.json({ error: 'Database error' }, { status: 500 })\n    }\n\n    if (!existingUser) {\n      console.log('User not found in users table, creating...')\n      \n      // Create user in our users table\n      const { data: newUser, error: createUserError } = await supabase\n        .from('users')\n        .insert({\n          id: user.id,\n          email: user.email || '',\n          subscription_tier: 'free'\n        })\n        .select()\n        .single()\n\n      if (createUserError) {\n        console.error('Error creating user:', createUserError)\n        return NextResponse.json({ error: 'Failed to create user' }, { status: 500 })\n      }\n\n      console.log('User created successfully:', newUser.id)\n    }\n\n    // Check if user has an organization\n    const { data: orgMembers, error: orgError } = await supabase\n      .from('organization_members')\n      .select('org_id')\n      .eq('user_id', user.id)\n      .limit(1)\n\n    if (orgError) {\n      console.error('Error checking organizations:', orgError)\n      return NextResponse.json({ error: 'Failed to check organizations' }, { status: 500 })\n    }\n\n    if (!orgMembers || orgMembers.length === 0) {\n      console.log('No organization found, creating default organization...')\n      \n      // Create default organization\n      const { data: newOrg, error: createOrgError } = await supabase\n        .from('organizations')\n        .insert({\n          name: `${user.email}'s Organization`,\n          owner_id: user.id\n        })\n        .select()\n        .single()\n\n      if (createOrgError) {\n        console.error('Error creating organization:', createOrgError)\n        return NextResponse.json({ error: 'Failed to create organization' }, { status: 500 })\n      }\n\n      // Add user as owner of the organization\n      const { error: memberError } = await supabase\n        .from('organization_members')\n        .insert({\n          org_id: newOrg.id,\n          user_id: user.id,\n          role: 'owner'\n        })\n\n      if (memberError) {\n        console.error('Error creating organization membership:', memberError)\n        return NextResponse.json({ error: 'Failed to create organization membership' }, { status: 500 })\n      }\n\n      console.log('Organization created successfully:', newOrg.id)\n    }\n\n    return NextResponse.json({ \n      success: true,\n      message: 'User and organization ensured'\n    })\n  } catch (error) {\n    console.error('Error ensuring user:', error)\n    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,2BAAwB,AAAD,EAAE;QAE1C,uBAAuB;QACvB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QAExE,IAAI,aAAa,CAAC,MAAM;YACtB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAe,GAAG;gBAAE,QAAQ;YAAI;QACpE;QAEA,0CAA0C;QAC1C,MAAM,EAAE,MAAM,YAAY,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SACpD,IAAI,CAAC,SACL,MAAM,CAAC,MACP,EAAE,CAAC,MAAM,KAAK,EAAE,EAChB,MAAM;QAET,IAAI,aAAa,UAAU,IAAI,KAAK,YAAY;YAC9C,QAAQ,KAAK,CAAC,wBAAwB;YACtC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAiB,GAAG;gBAAE,QAAQ;YAAI;QACtE;QAEA,IAAI,CAAC,cAAc;YACjB,QAAQ,GAAG,CAAC;YAEZ,iCAAiC;YACjC,MAAM,EAAE,MAAM,OAAO,EAAE,OAAO,eAAe,EAAE,GAAG,MAAM,SACrD,IAAI,CAAC,SACL,MAAM,CAAC;gBACN,IAAI,KAAK,EAAE;gBACX,OAAO,KAAK,KAAK,IAAI;gBACrB,mBAAmB;YACrB,GACC,MAAM,GACN,MAAM;YAET,IAAI,iBAAiB;gBACnB,QAAQ,KAAK,CAAC,wBAAwB;gBACtC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBAAE,OAAO;gBAAwB,GAAG;oBAAE,QAAQ;gBAAI;YAC7E;YAEA,QAAQ,GAAG,CAAC,8BAA8B,QAAQ,EAAE;QACtD;QAEA,oCAAoC;QACpC,MAAM,EAAE,MAAM,UAAU,EAAE,OAAO,QAAQ,EAAE,GAAG,MAAM,SACjD,IAAI,CAAC,wBACL,MAAM,CAAC,UACP,EAAE,CAAC,WAAW,KAAK,EAAE,EACrB,KAAK,CAAC;QAET,IAAI,UAAU;YACZ,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAgC,GAAG;gBAAE,QAAQ;YAAI;QACrF;QAEA,IAAI,CAAC,cAAc,WAAW,MAAM,KAAK,GAAG;YAC1C,QAAQ,GAAG,CAAC;YAEZ,8BAA8B;YAC9B,MAAM,EAAE,MAAM,MAAM,EAAE,OAAO,cAAc,EAAE,GAAG,MAAM,SACnD,IAAI,CAAC,iBACL,MAAM,CAAC;gBACN,MAAM,GAAG,KAAK,KAAK,CAAC,eAAe,CAAC;gBACpC,UAAU,KAAK,EAAE;YACnB,GACC,MAAM,GACN,MAAM;YAET,IAAI,gBAAgB;gBAClB,QAAQ,KAAK,CAAC,gCAAgC;gBAC9C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBAAE,OAAO;gBAAgC,GAAG;oBAAE,QAAQ;gBAAI;YACrF;YAEA,wCAAwC;YACxC,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,SAClC,IAAI,CAAC,wBACL,MAAM,CAAC;gBACN,QAAQ,OAAO,EAAE;gBACjB,SAAS,KAAK,EAAE;gBAChB,MAAM;YACR;YAEF,IAAI,aAAa;gBACf,QAAQ,KAAK,CAAC,2CAA2C;gBACzD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBAAE,OAAO;gBAA2C,GAAG;oBAAE,QAAQ;gBAAI;YAChG;YAEA,QAAQ,GAAG,CAAC,sCAAsC,OAAO,EAAE;QAC7D;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS;QACX;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAwB,GAAG;YAAE,QAAQ;QAAI;IAC7E;AACF", "debugId": null}}]}