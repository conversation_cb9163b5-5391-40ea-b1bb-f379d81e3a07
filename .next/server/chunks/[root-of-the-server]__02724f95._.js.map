{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 154, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/FlowUi/src/lib/supabase-server.ts"], "sourcesContent": ["import { createServerClient } from '@supabase/ssr'\nimport { cookies } from 'next/headers'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n\n// Server-side Supabase client for Server Components\nexport const createServerComponentClient = async () => {\n  const cookieStore = await cookies()\n  \n  return createServerClient(supabaseUrl, supabaseAnonKey, {\n    cookies: {\n      getAll() {\n        return cookieStore.getAll()\n      },\n      setAll(cookiesToSet) {\n        try {\n          cookiesToSet.forEach(({ name, value, options }) =>\n            cookieStore.set(name, value, options)\n          )\n        } catch {\n          // The `setAll` method was called from a Server Component.\n          // This can be ignored if you have middleware refreshing\n          // user sessions.\n        }\n      },\n    },\n  })\n}\n\n// Server-side Supabase client for Route Handlers\nexport const createRouteHandlerClient = (request: Request) => {\n  const response = new Response()\n  \n  return createServerClient(supabaseUrl, supabase<PERSON>non<PERSON>ey, {\n    cookies: {\n      getAll() {\n        const cookieHeader = request.headers.get('cookie')\n        if (!cookieHeader) return []\n        \n        return cookieHeader.split(';').map(cookie => {\n          const [name, value] = cookie.trim().split('=')\n          return { name, value }\n        })\n      },\n      setAll(cookiesToSet) {\n        cookiesToSet.forEach(({ name, value, options }) => {\n          response.headers.append('Set-Cookie', `${name}=${value}; ${Object.entries(options || {}).map(([k, v]) => `${k}=${v}`).join('; ')}`)\n        })\n      },\n    },\n  })\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AACA;;;AAEA,MAAM;AACN,MAAM;AAGC,MAAM,8BAA8B;IACzC,MAAM,cAAc,MAAM,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IAEhC,OAAO,CAAA,GAAA,2KAAA,CAAA,qBAAkB,AAAD,EAAE,aAAa,iBAAiB;QACtD,SAAS;YACP;gBACE,OAAO,YAAY,MAAM;YAC3B;YACA,QAAO,YAAY;gBACjB,IAAI;oBACF,aAAa,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,GAC5C,YAAY,GAAG,CAAC,MAAM,OAAO;gBAEjC,EAAE,OAAM;gBACN,0DAA0D;gBAC1D,wDAAwD;gBACxD,iBAAiB;gBACnB;YACF;QACF;IACF;AACF;AAGO,MAAM,2BAA2B,CAAC;IACvC,MAAM,WAAW,IAAI;IAErB,OAAO,CAAA,GAAA,2KAAA,CAAA,qBAAkB,AAAD,EAAE,aAAa,iBAAiB;QACtD,SAAS;YACP;gBACE,MAAM,eAAe,QAAQ,OAAO,CAAC,GAAG,CAAC;gBACzC,IAAI,CAAC,cAAc,OAAO,EAAE;gBAE5B,OAAO,aAAa,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA;oBACjC,MAAM,CAAC,MAAM,MAAM,GAAG,OAAO,IAAI,GAAG,KAAK,CAAC;oBAC1C,OAAO;wBAAE;wBAAM;oBAAM;gBACvB;YACF;YACA,QAAO,YAAY;gBACjB,aAAa,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE;oBAC5C,SAAS,OAAO,CAAC,MAAM,CAAC,cAAc,GAAG,KAAK,CAAC,EAAE,MAAM,EAAE,EAAE,OAAO,OAAO,CAAC,WAAW,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,GAAK,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,IAAI,CAAC,OAAO;gBACpI;YACF;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 211, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/FlowUi/src/app/api/debug/user/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { createRouteHandlerClient } from '@/lib/supabase-server'\n\nexport async function GET(request: NextRequest) {\n  try {\n    const supabase = createRouteHandlerClient(request)\n    \n    // Get the current user\n    const { data: { user }, error: authError } = await supabase.auth.getUser()\n    \n    if (authError) {\n      return NextResponse.json({ \n        error: 'Authentication error',\n        details: authError \n      }, { status: 401 })\n    }\n\n    if (!user) {\n      return NextResponse.json({ \n        error: 'No user found' \n      }, { status: 401 })\n    }\n\n    // Get user's organizations\n    const { data: orgMembers, error: orgError } = await supabase\n      .from('organization_members')\n      .select(`\n        org_id,\n        role,\n        organizations (\n          id,\n          name,\n          owner_id\n        )\n      `)\n      .eq('user_id', user.id)\n\n    // Get user record from our users table\n    const { data: userRecord, error: userError } = await supabase\n      .from('users')\n      .select('*')\n      .eq('id', user.id)\n      .single()\n\n    return NextResponse.json({\n      auth_user: {\n        id: user.id,\n        email: user.email,\n        created_at: user.created_at\n      },\n      user_record: userRecord,\n      user_record_error: userError,\n      organizations: orgMembers,\n      org_error: orgError,\n      debug_info: {\n        has_organizations: orgMembers && orgMembers.length > 0,\n        org_count: orgMembers ? orgMembers.length : 0\n      }\n    })\n  } catch (error) {\n    console.error('Debug user error:', error)\n    return NextResponse.json({ \n      error: 'Internal server error',\n      details: error instanceof Error ? error.message : 'Unknown error'\n    }, { status: 500 })\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,2BAAwB,AAAD,EAAE;QAE1C,uBAAuB;QACvB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QAExE,IAAI,WAAW;YACb,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,OAAO;gBACP,SAAS;YACX,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,OAAO;YACT,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,2BAA2B;QAC3B,MAAM,EAAE,MAAM,UAAU,EAAE,OAAO,QAAQ,EAAE,GAAG,MAAM,SACjD,IAAI,CAAC,wBACL,MAAM,CAAC,CAAC;;;;;;;;MAQT,CAAC,EACA,EAAE,CAAC,WAAW,KAAK,EAAE;QAExB,uCAAuC;QACvC,MAAM,EAAE,MAAM,UAAU,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAClD,IAAI,CAAC,SACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,KAAK,EAAE,EAChB,MAAM;QAET,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,WAAW;gBACT,IAAI,KAAK,EAAE;gBACX,OAAO,KAAK,KAAK;gBACjB,YAAY,KAAK,UAAU;YAC7B;YACA,aAAa;YACb,mBAAmB;YACnB,eAAe;YACf,WAAW;YACX,YAAY;gBACV,mBAAmB,cAAc,WAAW,MAAM,GAAG;gBACrD,WAAW,aAAa,WAAW,MAAM,GAAG;YAC9C;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qBAAqB;QACnC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,OAAO;YACP,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACpD,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF", "debugId": null}}]}