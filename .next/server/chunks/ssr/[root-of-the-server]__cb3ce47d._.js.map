{"version": 3, "sources": [], "sections": [{"offset": {"line": 117, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/FlowUi/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\nimport { createBrowserClient, createServerClient } from '@supabase/ssr'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n\n// Client-side Supabase client\nexport const createClientComponentClient = () => {\n  return createBrowserClient(supabaseUrl, supabaseAnonKey)\n}\n\n// Legacy client for backward compatibility (client-side only)\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey)\n\n// Database types (will be generated later)\nexport type Database = {\n  public: {\n    Tables: {\n      users: {\n        Row: {\n          id: string\n          email: string\n          created_at: string\n          subscription_tier: string\n        }\n        Insert: {\n          id?: string\n          email: string\n          created_at?: string\n          subscription_tier?: string\n        }\n        Update: {\n          id?: string\n          email?: string\n          created_at?: string\n          subscription_tier?: string\n        }\n      }\n      // More tables will be added as we implement them\n    }\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;;;AAEA,MAAM;AACN,MAAM;AAGC,MAAM,8BAA8B;IACzC,OAAO,CAAA,GAAA,0KAAA,CAAA,sBAAmB,AAAD,EAAE,aAAa;AAC1C;AAGO,MAAM,WAAW,CAAA,GAAA,uLAAA,CAAA,eAAY,AAAD,EAAE,aAAa", "debugId": null}}, {"offset": {"line": 136, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/FlowUi/src/lib/store.ts"], "sourcesContent": ["import { create } from 'zustand'\nimport { devtools, persist } from 'zustand/middleware'\n\n// User and Authentication Store\ninterface User {\n  id: string\n  email: string\n  subscription_tier: 'free' | 'pro' | 'team' | 'enterprise'\n}\n\ninterface AuthState {\n  user: User | null\n  isLoading: boolean\n  setUser: (user: User | null) => void\n  setLoading: (loading: boolean) => void\n  logout: () => void\n}\n\nexport const useAuthStore = create<AuthState>()(\n  devtools(\n    (set) => ({\n      user: null,\n      isLoading: true,\n      setUser: (user) => set({ user }),\n      setLoading: (isLoading) => set({ isLoading }),\n      logout: () => set({ user: null }),\n    }),\n    { name: 'auth-store' }\n  )\n)\n\n// UI Builder Store\ninterface UIComponent {\n  id: string\n  type: string\n  props: Record<string, any>\n  position: { x: number; y: number }\n  size: { width: number; height: number }\n  parent_id?: string\n  children?: string[]\n}\n\ninterface Project {\n  id: string\n  name: string\n  description?: string\n  webhook_url?: string\n  is_published: boolean\n  public_url?: string\n  components: UIComponent[]\n}\n\ninterface UIBuilderState {\n  currentProject: Project | null\n  selectedComponent: string | null\n  draggedComponent: UIComponent | null\n  isPreviewMode: boolean\n  setCurrentProject: (project: Project | null) => void\n  setSelectedComponent: (componentId: string | null) => void\n  setDraggedComponent: (component: UIComponent | null) => void\n  togglePreviewMode: () => void\n  addComponent: (component: UIComponent) => void\n  updateComponent: (componentId: string, updates: Partial<UIComponent>) => void\n  removeComponent: (componentId: string) => void\n}\n\nexport const useUIBuilderStore = create<UIBuilderState>()(\n  devtools(\n    (set, get) => ({\n      currentProject: null,\n      selectedComponent: null,\n      draggedComponent: null,\n      isPreviewMode: false,\n      setCurrentProject: (project) => set({ currentProject: project }),\n      setSelectedComponent: (componentId) => set({ selectedComponent: componentId }),\n      setDraggedComponent: (component) => set({ draggedComponent: component }),\n      togglePreviewMode: () => set((state) => ({ isPreviewMode: !state.isPreviewMode })),\n      addComponent: (component) =>\n        set((state) => {\n          if (!state.currentProject) return state\n          return {\n            currentProject: {\n              ...state.currentProject,\n              components: [...state.currentProject.components, component],\n            },\n          }\n        }),\n      updateComponent: (componentId, updates) =>\n        set((state) => {\n          if (!state.currentProject) return state\n          return {\n            currentProject: {\n              ...state.currentProject,\n              components: state.currentProject.components.map((comp) =>\n                comp.id === componentId ? { ...comp, ...updates } : comp\n              ),\n            },\n          }\n        }),\n      removeComponent: (componentId) =>\n        set((state) => {\n          if (!state.currentProject) return state\n          return {\n            currentProject: {\n              ...state.currentProject,\n              components: state.currentProject.components.filter((comp) => comp.id !== componentId),\n            },\n          }\n        }),\n    }),\n    { name: 'ui-builder-store' }\n  )\n)\n\n// API Testing Store\ninterface APIRequest {\n  id: string\n  name: string\n  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'\n  url: string\n  headers: Record<string, string>\n  body?: string\n}\n\ninterface APITestingState {\n  requests: APIRequest[]\n  currentRequest: APIRequest | null\n  isLoading: boolean\n  lastResponse: any\n  addRequest: (request: APIRequest) => void\n  updateRequest: (requestId: string, updates: Partial<APIRequest>) => void\n  removeRequest: (requestId: string) => void\n  setCurrentRequest: (request: APIRequest | null) => void\n  setLoading: (loading: boolean) => void\n  setLastResponse: (response: any) => void\n}\n\nexport const useAPITestingStore = create<APITestingState>()(\n  devtools(\n    persist(\n      (set) => ({\n        requests: [],\n        currentRequest: null,\n        isLoading: false,\n        lastResponse: null,\n        addRequest: (request) =>\n          set((state) => ({ requests: [...state.requests, request] })),\n        updateRequest: (requestId, updates) =>\n          set((state) => ({\n            requests: state.requests.map((req) =>\n              req.id === requestId ? { ...req, ...updates } : req\n            ),\n          })),\n        removeRequest: (requestId) =>\n          set((state) => ({\n            requests: state.requests.filter((req) => req.id !== requestId),\n          })),\n        setCurrentRequest: (request) => set({ currentRequest: request }),\n        setLoading: (isLoading) => set({ isLoading }),\n        setLastResponse: (lastResponse) => set({ lastResponse }),\n      }),\n      {\n        name: 'api-testing-storage',\n        partialize: (state) => ({ requests: state.requests }),\n      }\n    ),\n    { name: 'api-testing-store' }\n  )\n)\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;AAiBO,MAAM,eAAe,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,IAC/B,CAAA,GAAA,6IAAA,CAAA,WAAQ,AAAD,EACL,CAAC,MAAQ,CAAC;QACR,MAAM;QACN,WAAW;QACX,SAAS,CAAC,OAAS,IAAI;gBAAE;YAAK;QAC9B,YAAY,CAAC,YAAc,IAAI;gBAAE;YAAU;QAC3C,QAAQ,IAAM,IAAI;gBAAE,MAAM;YAAK;IACjC,CAAC,GACD;IAAE,MAAM;AAAa;AAuClB,MAAM,oBAAoB,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,IACpC,CAAA,GAAA,6IAAA,CAAA,WAAQ,AAAD,EACL,CAAC,KAAK,MAAQ,CAAC;QACb,gBAAgB;QAChB,mBAAmB;QACnB,kBAAkB;QAClB,eAAe;QACf,mBAAmB,CAAC,UAAY,IAAI;gBAAE,gBAAgB;YAAQ;QAC9D,sBAAsB,CAAC,cAAgB,IAAI;gBAAE,mBAAmB;YAAY;QAC5E,qBAAqB,CAAC,YAAc,IAAI;gBAAE,kBAAkB;YAAU;QACtE,mBAAmB,IAAM,IAAI,CAAC,QAAU,CAAC;oBAAE,eAAe,CAAC,MAAM,aAAa;gBAAC,CAAC;QAChF,cAAc,CAAC,YACb,IAAI,CAAC;gBACH,IAAI,CAAC,MAAM,cAAc,EAAE,OAAO;gBAClC,OAAO;oBACL,gBAAgB;wBACd,GAAG,MAAM,cAAc;wBACvB,YAAY;+BAAI,MAAM,cAAc,CAAC,UAAU;4BAAE;yBAAU;oBAC7D;gBACF;YACF;QACF,iBAAiB,CAAC,aAAa,UAC7B,IAAI,CAAC;gBACH,IAAI,CAAC,MAAM,cAAc,EAAE,OAAO;gBAClC,OAAO;oBACL,gBAAgB;wBACd,GAAG,MAAM,cAAc;wBACvB,YAAY,MAAM,cAAc,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,OAC/C,KAAK,EAAE,KAAK,cAAc;gCAAE,GAAG,IAAI;gCAAE,GAAG,OAAO;4BAAC,IAAI;oBAExD;gBACF;YACF;QACF,iBAAiB,CAAC,cAChB,IAAI,CAAC;gBACH,IAAI,CAAC,MAAM,cAAc,EAAE,OAAO;gBAClC,OAAO;oBACL,gBAAgB;wBACd,GAAG,MAAM,cAAc;wBACvB,YAAY,MAAM,cAAc,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,OAAS,KAAK,EAAE,KAAK;oBAC3E;gBACF;YACF;IACJ,CAAC,GACD;IAAE,MAAM;AAAmB;AA2BxB,MAAM,qBAAqB,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,IACrC,CAAA,GAAA,6IAAA,CAAA,WAAQ,AAAD,EACL,CAAA,GAAA,6IAAA,CAAA,UAAO,AAAD,EACJ,CAAC,MAAQ,CAAC;QACR,UAAU,EAAE;QACZ,gBAAgB;QAChB,WAAW;QACX,cAAc;QACd,YAAY,CAAC,UACX,IAAI,CAAC,QAAU,CAAC;oBAAE,UAAU;2BAAI,MAAM,QAAQ;wBAAE;qBAAQ;gBAAC,CAAC;QAC5D,eAAe,CAAC,WAAW,UACzB,IAAI,CAAC,QAAU,CAAC;oBACd,UAAU,MAAM,QAAQ,CAAC,GAAG,CAAC,CAAC,MAC5B,IAAI,EAAE,KAAK,YAAY;4BAAE,GAAG,GAAG;4BAAE,GAAG,OAAO;wBAAC,IAAI;gBAEpD,CAAC;QACH,eAAe,CAAC,YACd,IAAI,CAAC,QAAU,CAAC;oBACd,UAAU,MAAM,QAAQ,CAAC,MAAM,CAAC,CAAC,MAAQ,IAAI,EAAE,KAAK;gBACtD,CAAC;QACH,mBAAmB,CAAC,UAAY,IAAI;gBAAE,gBAAgB;YAAQ;QAC9D,YAAY,CAAC,YAAc,IAAI;gBAAE;YAAU;QAC3C,iBAAiB,CAAC,eAAiB,IAAI;gBAAE;YAAa;IACxD,CAAC,GACD;IACE,MAAM;IACN,YAAY,CAAC,QAAU,CAAC;YAAE,UAAU,MAAM,QAAQ;QAAC,CAAC;AACtD,IAEF;IAAE,MAAM;AAAoB", "debugId": null}}, {"offset": {"line": 254, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/FlowUi/src/hooks/useAuth.ts"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { createClientComponentClient } from '@/lib/supabase'\nimport { useAuthStore } from '@/lib/store'\nimport { User } from '@/types'\n\nexport function useAuth() {\n  const router = useRouter()\n  const supabase = createClientComponentClient()\n  const { user, isLoading, setUser, setLoading } = useAuthStore()\n  const [isHydrated, setIsHydrated] = useState(false)\n\n  useEffect(() => {\n    // Set hydrated flag\n    setIsHydrated(true)\n\n    // Get initial session\n    const getInitialSession = async () => {\n      try {\n        console.log('🔍 Getting initial session...')\n        const { data: { session }, error } = await supabase.auth.getSession()\n\n        if (error) {\n          console.error('❌ Error getting session:', error)\n          setUser(null)\n        } else if (session?.user) {\n          console.log('✅ Session found, setting user:', session.user.email)\n          // For now, create user from session data (database setup not required)\n          setUser({\n            id: session.user.id,\n            email: session.user.email!,\n            subscription_tier: 'free',\n            created_at: session.user.created_at || new Date().toISOString(),\n            updated_at: new Date().toISOString(),\n          })\n        } else {\n          console.log('ℹ️ No session found')\n          setUser(null)\n        }\n      } catch (error) {\n        console.error('❌ Error in getInitialSession:', error)\n        setUser(null)\n      } finally {\n        console.log('🏁 Setting loading to false')\n        setLoading(false)\n      }\n    }\n\n    getInitialSession()\n\n    // Listen for auth changes\n    const { data: { subscription } } = supabase.auth.onAuthStateChange(\n      async (event, session) => {\n        if (event === 'SIGNED_IN' && session?.user) {\n          // Handle sign in - create user from session data\n          setUser({\n            id: session.user.id,\n            email: session.user.email!,\n            subscription_tier: 'free',\n            created_at: session.user.created_at || new Date().toISOString(),\n            updated_at: new Date().toISOString(),\n          })\n          router.push('/dashboard')\n        } else if (event === 'SIGNED_OUT') {\n          // Handle sign out\n          setUser(null)\n          router.push('/')\n        }\n      }\n    )\n\n    return () => {\n      subscription.unsubscribe()\n    }\n  }, [supabase, setUser, setLoading, router])\n\n  const signIn = async (email: string, password: string) => {\n    try {\n      setLoading(true)\n      const { error } = await supabase.auth.signInWithPassword({\n        email,\n        password,\n      })\n\n      if (error) throw error\n      \n      return { success: true }\n    } catch (error: any) {\n      return { \n        success: false, \n        error: error.message || 'An error occurred during sign in' \n      }\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const signUp = async (email: string, password: string) => {\n    try {\n      setLoading(true)\n      const { error } = await supabase.auth.signUp({\n        email,\n        password,\n        options: {\n          emailRedirectTo: `${window.location.origin}/auth/callback`,\n        },\n      })\n\n      if (error) throw error\n      \n      return { success: true }\n    } catch (error: any) {\n      return { \n        success: false, \n        error: error.message || 'An error occurred during sign up' \n      }\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const signOut = async () => {\n    try {\n      setLoading(true)\n      const { error } = await supabase.auth.signOut()\n      if (error) throw error\n      \n      return { success: true }\n    } catch (error: any) {\n      return { \n        success: false, \n        error: error.message || 'An error occurred during sign out' \n      }\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const signInWithProvider = async (provider: 'google' | 'github') => {\n    try {\n      setLoading(true)\n      const { error } = await supabase.auth.signInWithOAuth({\n        provider,\n        options: {\n          redirectTo: `${window.location.origin}/auth/callback`,\n        },\n      })\n\n      if (error) throw error\n      \n      return { success: true }\n    } catch (error: any) {\n      return { \n        success: false, \n        error: error.message || 'An error occurred during OAuth sign in' \n      }\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const resetPassword = async (email: string) => {\n    try {\n      setLoading(true)\n      const { error } = await supabase.auth.resetPasswordForEmail(email, {\n        redirectTo: `${window.location.origin}/auth/reset-password`,\n      })\n\n      if (error) throw error\n      \n      return { success: true }\n    } catch (error: any) {\n      return { \n        success: false, \n        error: error.message || 'An error occurred during password reset' \n      }\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  return {\n    user,\n    isLoading: isLoading || !isHydrated,\n    signIn,\n    signUp,\n    signOut,\n    signInWithProvider,\n    resetPassword,\n    isAuthenticated: isHydrated && !!user,\n  }\n}\n"], "names": [], "mappings": ";;;AAEA;AACA;AACA;AACA;AALA;;;;;AAQO,SAAS;IACd,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,sHAAA,CAAA,8BAA2B,AAAD;IAC3C,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,mHAAA,CAAA,eAAY,AAAD;IAC5D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,oBAAoB;QACpB,cAAc;QAEd,sBAAsB;QACtB,MAAM,oBAAoB;YACxB,IAAI;gBACF,QAAQ,GAAG,CAAC;gBACZ,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU;gBAEnE,IAAI,OAAO;oBACT,QAAQ,KAAK,CAAC,4BAA4B;oBAC1C,QAAQ;gBACV,OAAO,IAAI,SAAS,MAAM;oBACxB,QAAQ,GAAG,CAAC,kCAAkC,QAAQ,IAAI,CAAC,KAAK;oBAChE,uEAAuE;oBACvE,QAAQ;wBACN,IAAI,QAAQ,IAAI,CAAC,EAAE;wBACnB,OAAO,QAAQ,IAAI,CAAC,KAAK;wBACzB,mBAAmB;wBACnB,YAAY,QAAQ,IAAI,CAAC,UAAU,IAAI,IAAI,OAAO,WAAW;wBAC7D,YAAY,IAAI,OAAO,WAAW;oBACpC;gBACF,OAAO;oBACL,QAAQ,GAAG,CAAC;oBACZ,QAAQ;gBACV;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,iCAAiC;gBAC/C,QAAQ;YACV,SAAU;gBACR,QAAQ,GAAG,CAAC;gBACZ,WAAW;YACb;QACF;QAEA;QAEA,0BAA0B;QAC1B,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,EAAE,GAAG,SAAS,IAAI,CAAC,iBAAiB,CAChE,OAAO,OAAO;YACZ,IAAI,UAAU,eAAe,SAAS,MAAM;gBAC1C,iDAAiD;gBACjD,QAAQ;oBACN,IAAI,QAAQ,IAAI,CAAC,EAAE;oBACnB,OAAO,QAAQ,IAAI,CAAC,KAAK;oBACzB,mBAAmB;oBACnB,YAAY,QAAQ,IAAI,CAAC,UAAU,IAAI,IAAI,OAAO,WAAW;oBAC7D,YAAY,IAAI,OAAO,WAAW;gBACpC;gBACA,OAAO,IAAI,CAAC;YACd,OAAO,IAAI,UAAU,cAAc;gBACjC,kBAAkB;gBAClB,QAAQ;gBACR,OAAO,IAAI,CAAC;YACd;QACF;QAGF,OAAO;YACL,aAAa,WAAW;QAC1B;IACF,GAAG;QAAC;QAAU;QAAS;QAAY;KAAO;IAE1C,MAAM,SAAS,OAAO,OAAe;QACnC,IAAI;YACF,WAAW;YACX,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,kBAAkB,CAAC;gBACvD;gBACA;YACF;YAEA,IAAI,OAAO,MAAM;YAEjB,OAAO;gBAAE,SAAS;YAAK;QACzB,EAAE,OAAO,OAAY;YACnB,OAAO;gBACL,SAAS;gBACT,OAAO,MAAM,OAAO,IAAI;YAC1B;QACF,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,SAAS,OAAO,OAAe;QACnC,IAAI;YACF,WAAW;YACX,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,MAAM,CAAC;gBAC3C;gBACA;gBACA,SAAS;oBACP,iBAAiB,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,cAAc,CAAC;gBAC5D;YACF;YAEA,IAAI,OAAO,MAAM;YAEjB,OAAO;gBAAE,SAAS;YAAK;QACzB,EAAE,OAAO,OAAY;YACnB,OAAO;gBACL,SAAS;gBACT,OAAO,MAAM,OAAO,IAAI;YAC1B;QACF,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,UAAU;QACd,IAAI;YACF,WAAW;YACX,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;YAC7C,IAAI,OAAO,MAAM;YAEjB,OAAO;gBAAE,SAAS;YAAK;QACzB,EAAE,OAAO,OAAY;YACnB,OAAO;gBACL,SAAS;gBACT,OAAO,MAAM,OAAO,IAAI;YAC1B;QACF,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,qBAAqB,OAAO;QAChC,IAAI;YACF,WAAW;YACX,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,eAAe,CAAC;gBACpD;gBACA,SAAS;oBACP,YAAY,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,cAAc,CAAC;gBACvD;YACF;YAEA,IAAI,OAAO,MAAM;YAEjB,OAAO;gBAAE,SAAS;YAAK;QACzB,EAAE,OAAO,OAAY;YACnB,OAAO;gBACL,SAAS;gBACT,OAAO,MAAM,OAAO,IAAI;YAC1B;QACF,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,gBAAgB,OAAO;QAC3B,IAAI;YACF,WAAW;YACX,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,qBAAqB,CAAC,OAAO;gBACjE,YAAY,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,oBAAoB,CAAC;YAC7D;YAEA,IAAI,OAAO,MAAM;YAEjB,OAAO;gBAAE,SAAS;YAAK;QACzB,EAAE,OAAO,OAAY;YACnB,OAAO;gBACL,SAAS;gBACT,OAAO,MAAM,OAAO,IAAI;YAC1B;QACF,SAAU;YACR,WAAW;QACb;IACF;IAEA,OAAO;QACL;QACA,WAAW,aAAa,CAAC;QACzB;QACA;QACA;QACA;QACA;QACA,iBAAiB,cAAc,CAAC,CAAC;IACnC;AACF", "debugId": null}}, {"offset": {"line": 448, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/FlowUi/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function generateId(): string {\n  return Math.random().toString(36).substr(2, 9)\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAC9C", "debugId": null}}, {"offset": {"line": 466, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/FlowUi/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 521, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/FlowUi/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 616, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/FlowUi/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 660, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/FlowUi/src/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Tabs({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Root>) {\n  return (\n    <TabsPrimitive.Root\n      data-slot=\"tabs\"\n      className={cn(\"flex flex-col gap-2\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TabsList({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.List>) {\n  return (\n    <TabsPrimitive.List\n      data-slot=\"tabs-list\"\n      className={cn(\n        \"bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TabsTrigger({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Trigger>) {\n  return (\n    <TabsPrimitive.Trigger\n      data-slot=\"tabs-trigger\"\n      className={cn(\n        \"data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TabsContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof TabsPrimitive.Content>) {\n  return (\n    <TabsPrimitive.Content\n      data-slot=\"tabs-content\"\n      className={cn(\"flex-1 outline-none\", className)}\n      {...props}\n    />\n  )\n}\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,KAAK,EACZ,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,8OAAC,gKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,8OAAC,gKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uGACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,gKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mqBACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,gKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 722, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/FlowUi/src/components/api-testing/api-testing-header.tsx"], "sourcesContent": ["'use client'\n\nimport { But<PERSON> } from '@/components/ui/button'\nimport { Badge } from '@/components/ui/badge'\nimport { useRouter } from 'next/navigation'\nimport { \n  ArrowLeft,\n  Plus,\n  Save,\n  Send,\n  Loader2\n} from 'lucide-react'\nimport { APIRequest } from '@/types'\n\ninterface APITestingHeaderProps {\n  onNewRequest: () => void\n  onSaveRequest: () => void\n  onSendRequest: () => void\n  isLoading: boolean\n  currentRequest: APIRequest | null\n}\n\nexport function APITestingHeader({\n  onNewRequest,\n  onSaveRequest,\n  onSendRequest,\n  isLoading,\n  currentRequest\n}: APITestingHeaderProps) {\n  const router = useRouter()\n\n  const handleBackToDashboard = () => {\n    router.push('/dashboard')\n  }\n\n  return (\n    <header className=\"h-16 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between px-4\">\n      {/* Left Section */}\n      <div className=\"flex items-center gap-4\">\n        <Button\n          variant=\"ghost\"\n          size=\"sm\"\n          onClick={handleBackToDashboard}\n          className=\"flex items-center gap-2\"\n        >\n          <ArrowLeft className=\"w-4 h-4\" />\n          Back\n        </Button>\n        \n        <div className=\"flex items-center gap-2\">\n          <div className=\"w-6 h-6 bg-gradient-to-r from-green-600 to-blue-600 rounded\"></div>\n          <span className=\"font-semibold\">API Testing</span>\n        </div>\n\n        {currentRequest && (\n          <div className=\"text-sm text-muted-foreground\">\n            {currentRequest.name}\n          </div>\n        )}\n      </div>\n\n      {/* Center Section - Quick Actions */}\n      <div className=\"flex items-center gap-2\">\n        <Button\n          variant=\"ghost\"\n          size=\"sm\"\n          onClick={onNewRequest}\n          className=\"flex items-center gap-2\"\n        >\n          <Plus className=\"w-4 h-4\" />\n          New\n        </Button>\n\n        {currentRequest && (\n          <Button\n            variant=\"ghost\"\n            size=\"sm\"\n            onClick={onSaveRequest}\n            className=\"flex items-center gap-2\"\n          >\n            <Save className=\"w-4 h-4\" />\n            Save\n          </Button>\n        )}\n      </div>\n\n      {/* Right Section */}\n      <div className=\"flex items-center gap-2\">\n        {currentRequest && (\n          <>\n            <Badge variant=\"secondary\" className=\"text-xs\">\n              {currentRequest.method}\n            </Badge>\n            \n            <Button\n              onClick={onSendRequest}\n              disabled={isLoading || !currentRequest.url}\n              className=\"flex items-center gap-2\"\n            >\n              {isLoading ? (\n                <>\n                  <Loader2 className=\"w-4 h-4 animate-spin\" />\n                  Sending...\n                </>\n              ) : (\n                <>\n                  <Send className=\"w-4 h-4\" />\n                  Send\n                </>\n              )}\n            </Button>\n          </>\n        )}\n      </div>\n    </header>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;;AAsBO,SAAS,iBAAiB,EAC/B,YAAY,EACZ,aAAa,EACb,aAAa,EACb,SAAS,EACT,cAAc,EACQ;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,wBAAwB;QAC5B,OAAO,IAAI,CAAC;IACd;IAEA,qBACE,8OAAC;QAAO,WAAU;;0BAEhB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS;wBACT,WAAU;;0CAEV,8OAAC,gNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;4BAAY;;;;;;;kCAInC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAK,WAAU;0CAAgB;;;;;;;;;;;;oBAGjC,gCACC,8OAAC;wBAAI,WAAU;kCACZ,eAAe,IAAI;;;;;;;;;;;;0BAM1B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS;wBACT,WAAU;;0CAEV,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAY;;;;;;;oBAI7B,gCACC,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS;wBACT,WAAU;;0CAEV,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAY;;;;;;;;;;;;;0BAOlC,8OAAC;gBAAI,WAAU;0BACZ,gCACC;;sCACE,8OAAC,iIAAA,CAAA,QAAK;4BAAC,SAAQ;4BAAY,WAAU;sCAClC,eAAe,MAAM;;;;;;sCAGxB,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAS;4BACT,UAAU,aAAa,CAAC,eAAe,GAAG;4BAC1C,WAAU;sCAET,0BACC;;kDACE,8OAAC,iNAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;oCAAyB;;6DAI9C;;kDACE,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAY;;;;;;;;;;;;;;;;;;;;;AAU9C", "debugId": null}}, {"offset": {"line": 921, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/FlowUi/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 945, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/FlowUi/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,8OAAC,iKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 971, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/FlowUi/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,kKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,wNAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,SAAsB;kBACrB,cAAA,8OAAC,kKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,kKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIT;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kKAAA,CAAA,gBAA6B;8BAC5B,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,8OAAC,kKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;AAEA,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,8OAAC,kKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,8OAAC,kKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,kKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,wNAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC", "debugId": null}}, {"offset": {"line": 1194, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/FlowUi/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\n  return (\n    <textarea\n      data-slot=\"textarea\"\n      className={cn(\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAyC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1217, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/FlowUi/src/components/api-testing/request-builder.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'\nimport { Textarea } from '@/components/ui/textarea'\nimport { Button } from '@/components/ui/button'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'\nimport { Badge } from '@/components/ui/badge'\nimport { Plus, X, Code, FileText } from 'lucide-react'\nimport { APIRequest, HTTPMethod } from '@/types'\n\ninterface RequestBuilderProps {\n  request: APIRequest\n  onUpdateRequest: (updates: Partial<APIRequest>) => void\n}\n\nexport function RequestBuilder({ request, onUpdateRequest }: RequestBuilderProps) {\n  const [newHeader<PERSON>ey, setNewHeaderKey] = useState('')\n  const [newHeaderValue, setNewHeaderValue] = useState('')\n\n  const handleMethodChange = (method: HTTPMethod) => {\n    onUpdateRequest({ method })\n  }\n\n  const handleUrlChange = (url: string) => {\n    onUpdateRequest({ url })\n  }\n\n  const handleNameChange = (name: string) => {\n    onUpdateRequest({ name })\n  }\n\n  const handleAddHeader = () => {\n    if (newHeaderKey && newHeaderValue) {\n      onUpdateRequest({\n        headers: {\n          ...request.headers,\n          [newHeaderKey]: newHeaderValue\n        }\n      })\n      setNewHeaderKey('')\n      setNewHeaderValue('')\n    }\n  }\n\n  const handleRemoveHeader = (key: string) => {\n    const newHeaders = { ...request.headers }\n    delete newHeaders[key]\n    onUpdateRequest({ headers: newHeaders })\n  }\n\n  const handleHeaderValueChange = (key: string, value: string) => {\n    onUpdateRequest({\n      headers: {\n        ...request.headers,\n        [key]: value\n      }\n    })\n  }\n\n  const handleBodyChange = (body: string) => {\n    onUpdateRequest({ body })\n  }\n\n  const formatJSON = () => {\n    try {\n      const formatted = JSON.stringify(JSON.parse(request.body || ''), null, 2)\n      onUpdateRequest({ body: formatted })\n    } catch (error) {\n      // Invalid JSON, do nothing\n    }\n  }\n\n  const addCommonHeaders = (headerType: string) => {\n    const commonHeaders: Record<string, Record<string, string>> = {\n      'json': { 'Content-Type': 'application/json' },\n      'form': { 'Content-Type': 'application/x-www-form-urlencoded' },\n      'auth': { 'Authorization': 'Bearer YOUR_TOKEN_HERE' }\n    }\n\n    if (commonHeaders[headerType]) {\n      onUpdateRequest({\n        headers: {\n          ...request.headers,\n          ...commonHeaders[headerType]\n        }\n      })\n    }\n  }\n\n  return (\n    <div className=\"h-full overflow-auto p-4 space-y-4\">\n      {/* Request Name */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"text-sm\">Request Details</CardTitle>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          <div className=\"space-y-2\">\n            <Label htmlFor=\"request-name\">Request Name</Label>\n            <Input\n              id=\"request-name\"\n              value={request.name}\n              onChange={(e) => handleNameChange(e.target.value)}\n              placeholder=\"My API Request\"\n            />\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* URL and Method */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"text-sm\">Request URL</CardTitle>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          <div className=\"flex gap-2\">\n            <Select value={request.method} onValueChange={handleMethodChange}>\n              <SelectTrigger className=\"w-32\">\n                <SelectValue />\n              </SelectTrigger>\n              <SelectContent>\n                <SelectItem value=\"GET\">GET</SelectItem>\n                <SelectItem value=\"POST\">POST</SelectItem>\n                <SelectItem value=\"PUT\">PUT</SelectItem>\n                <SelectItem value=\"DELETE\">DELETE</SelectItem>\n                <SelectItem value=\"PATCH\">PATCH</SelectItem>\n                <SelectItem value=\"HEAD\">HEAD</SelectItem>\n                <SelectItem value=\"OPTIONS\">OPTIONS</SelectItem>\n              </SelectContent>\n            </Select>\n            \n            <Input\n              value={request.url}\n              onChange={(e) => handleUrlChange(e.target.value)}\n              placeholder=\"https://api.example.com/endpoint\"\n              className=\"flex-1\"\n            />\n          </div>\n          \n          {/* Quick URL Templates */}\n          <div className=\"flex gap-2 flex-wrap\">\n            <Button\n              variant=\"outline\"\n              size=\"sm\"\n              onClick={() => handleUrlChange('https://jsonplaceholder.typicode.com/posts')}\n            >\n              JSONPlaceholder\n            </Button>\n            <Button\n              variant=\"outline\"\n              size=\"sm\"\n              onClick={() => handleUrlChange('https://httpbin.org/get')}\n            >\n              HTTPBin\n            </Button>\n            <Button\n              variant=\"outline\"\n              size=\"sm\"\n              onClick={() => handleUrlChange('https://your-n8n-instance.com/webhook/your-webhook-id')}\n            >\n              N8N Webhook\n            </Button>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Headers and Body */}\n      <Tabs defaultValue=\"headers\" className=\"space-y-4\">\n        <TabsList className=\"grid w-full grid-cols-3\">\n          <TabsTrigger value=\"headers\">Headers</TabsTrigger>\n          <TabsTrigger value=\"body\" disabled={!['POST', 'PUT', 'PATCH'].includes(request.method)}>\n            Body\n          </TabsTrigger>\n          <TabsTrigger value=\"auth\">Auth</TabsTrigger>\n        </TabsList>\n\n        <TabsContent value=\"headers\">\n          <Card>\n            <CardHeader>\n              <div className=\"flex items-center justify-between\">\n                <CardTitle className=\"text-sm\">Headers</CardTitle>\n                <div className=\"flex gap-2\">\n                  <Button\n                    variant=\"outline\"\n                    size=\"sm\"\n                    onClick={() => addCommonHeaders('json')}\n                  >\n                    + JSON\n                  </Button>\n                  <Button\n                    variant=\"outline\"\n                    size=\"sm\"\n                    onClick={() => addCommonHeaders('form')}\n                  >\n                    + Form\n                  </Button>\n                </div>\n              </div>\n            </CardHeader>\n            <CardContent className=\"space-y-4\">\n              {/* Existing Headers */}\n              {Object.entries(request.headers).map(([key, value]) => (\n                <div key={key} className=\"flex gap-2 items-center\">\n                  <Input value={key} disabled className=\"flex-1\" />\n                  <Input\n                    value={value}\n                    onChange={(e) => handleHeaderValueChange(key, e.target.value)}\n                    className=\"flex-1\"\n                  />\n                  <Button\n                    variant=\"ghost\"\n                    size=\"sm\"\n                    onClick={() => handleRemoveHeader(key)}\n                  >\n                    <X className=\"w-4 h-4\" />\n                  </Button>\n                </div>\n              ))}\n\n              {/* Add New Header */}\n              <div className=\"flex gap-2 items-center\">\n                <Input\n                  value={newHeaderKey}\n                  onChange={(e) => setNewHeaderKey(e.target.value)}\n                  placeholder=\"Header name\"\n                  className=\"flex-1\"\n                />\n                <Input\n                  value={newHeaderValue}\n                  onChange={(e) => setNewHeaderValue(e.target.value)}\n                  placeholder=\"Header value\"\n                  className=\"flex-1\"\n                />\n                <Button\n                  variant=\"outline\"\n                  size=\"sm\"\n                  onClick={handleAddHeader}\n                  disabled={!newHeaderKey || !newHeaderValue}\n                >\n                  <Plus className=\"w-4 h-4\" />\n                </Button>\n              </div>\n            </CardContent>\n          </Card>\n        </TabsContent>\n\n        <TabsContent value=\"body\">\n          <Card>\n            <CardHeader>\n              <div className=\"flex items-center justify-between\">\n                <CardTitle className=\"text-sm\">Request Body</CardTitle>\n                <div className=\"flex gap-2\">\n                  <Button\n                    variant=\"outline\"\n                    size=\"sm\"\n                    onClick={formatJSON}\n                  >\n                    <Code className=\"w-4 h-4 mr-2\" />\n                    Format JSON\n                  </Button>\n                </div>\n              </div>\n            </CardHeader>\n            <CardContent>\n              <Textarea\n                value={request.body || ''}\n                onChange={(e) => handleBodyChange(e.target.value)}\n                placeholder='{\"key\": \"value\"}'\n                className=\"min-h-[200px] font-mono text-sm\"\n              />\n              \n              {/* Quick Body Templates */}\n              <div className=\"mt-4 flex gap-2 flex-wrap\">\n                <Button\n                  variant=\"outline\"\n                  size=\"sm\"\n                  onClick={() => handleBodyChange('{\\n  \"title\": \"Sample Title\",\\n  \"body\": \"Sample content\",\\n  \"userId\": 1\\n}')}\n                >\n                  JSON Sample\n                </Button>\n                <Button\n                  variant=\"outline\"\n                  size=\"sm\"\n                  onClick={() => handleBodyChange('name=John&email=<EMAIL>')}\n                >\n                  Form Data\n                </Button>\n              </div>\n            </CardContent>\n          </Card>\n        </TabsContent>\n\n        <TabsContent value=\"auth\">\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"text-sm\">Authentication</CardTitle>\n            </CardHeader>\n            <CardContent className=\"space-y-4\">\n              <div className=\"text-sm text-muted-foreground\">\n                Add authentication headers manually in the Headers tab, or use these quick options:\n              </div>\n              \n              <div className=\"flex gap-2 flex-wrap\">\n                <Button\n                  variant=\"outline\"\n                  size=\"sm\"\n                  onClick={() => addCommonHeaders('auth')}\n                >\n                  + Bearer Token\n                </Button>\n                <Button\n                  variant=\"outline\"\n                  size=\"sm\"\n                  onClick={() => onUpdateRequest({\n                    headers: {\n                      ...request.headers,\n                      'X-API-Key': 'YOUR_API_KEY_HERE'\n                    }\n                  })}\n                >\n                  + API Key\n                </Button>\n              </div>\n            </CardContent>\n          </Card>\n        </TabsContent>\n      </Tabs>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAXA;;;;;;;;;;;AAmBO,SAAS,eAAe,EAAE,OAAO,EAAE,eAAe,EAAuB;IAC9E,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,MAAM,qBAAqB,CAAC;QAC1B,gBAAgB;YAAE;QAAO;IAC3B;IAEA,MAAM,kBAAkB,CAAC;QACvB,gBAAgB;YAAE;QAAI;IACxB;IAEA,MAAM,mBAAmB,CAAC;QACxB,gBAAgB;YAAE;QAAK;IACzB;IAEA,MAAM,kBAAkB;QACtB,IAAI,gBAAgB,gBAAgB;YAClC,gBAAgB;gBACd,SAAS;oBACP,GAAG,QAAQ,OAAO;oBAClB,CAAC,aAAa,EAAE;gBAClB;YACF;YACA,gBAAgB;YAChB,kBAAkB;QACpB;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,MAAM,aAAa;YAAE,GAAG,QAAQ,OAAO;QAAC;QACxC,OAAO,UAAU,CAAC,IAAI;QACtB,gBAAgB;YAAE,SAAS;QAAW;IACxC;IAEA,MAAM,0BAA0B,CAAC,KAAa;QAC5C,gBAAgB;YACd,SAAS;gBACP,GAAG,QAAQ,OAAO;gBAClB,CAAC,IAAI,EAAE;YACT;QACF;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,gBAAgB;YAAE;QAAK;IACzB;IAEA,MAAM,aAAa;QACjB,IAAI;YACF,MAAM,YAAY,KAAK,SAAS,CAAC,KAAK,KAAK,CAAC,QAAQ,IAAI,IAAI,KAAK,MAAM;YACvE,gBAAgB;gBAAE,MAAM;YAAU;QACpC,EAAE,OAAO,OAAO;QACd,2BAA2B;QAC7B;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,gBAAwD;YAC5D,QAAQ;gBAAE,gBAAgB;YAAmB;YAC7C,QAAQ;gBAAE,gBAAgB;YAAoC;YAC9D,QAAQ;gBAAE,iBAAiB;YAAyB;QACtD;QAEA,IAAI,aAAa,CAAC,WAAW,EAAE;YAC7B,gBAAgB;gBACd,SAAS;oBACP,GAAG,QAAQ,OAAO;oBAClB,GAAG,aAAa,CAAC,WAAW;gBAC9B;YACF;QACF;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;sCAAU;;;;;;;;;;;kCAEjC,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;kCACrB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAe;;;;;;8CAC9B,8OAAC,iIAAA,CAAA,QAAK;oCACJ,IAAG;oCACH,OAAO,QAAQ,IAAI;oCACnB,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;oCAChD,aAAY;;;;;;;;;;;;;;;;;;;;;;;0BAOpB,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;sCAAU;;;;;;;;;;;kCAEjC,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCAAC,OAAO,QAAQ,MAAM;wCAAE,eAAe;;0DAC5C,8OAAC,kIAAA,CAAA,gBAAa;gDAAC,WAAU;0DACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;0DAEd,8OAAC,kIAAA,CAAA,gBAAa;;kEACZ,8OAAC,kIAAA,CAAA,aAAU;wDAAC,OAAM;kEAAM;;;;;;kEACxB,8OAAC,kIAAA,CAAA,aAAU;wDAAC,OAAM;kEAAO;;;;;;kEACzB,8OAAC,kIAAA,CAAA,aAAU;wDAAC,OAAM;kEAAM;;;;;;kEACxB,8OAAC,kIAAA,CAAA,aAAU;wDAAC,OAAM;kEAAS;;;;;;kEAC3B,8OAAC,kIAAA,CAAA,aAAU;wDAAC,OAAM;kEAAQ;;;;;;kEAC1B,8OAAC,kIAAA,CAAA,aAAU;wDAAC,OAAM;kEAAO;;;;;;kEACzB,8OAAC,kIAAA,CAAA,aAAU;wDAAC,OAAM;kEAAU;;;;;;;;;;;;;;;;;;kDAIhC,8OAAC,iIAAA,CAAA,QAAK;wCACJ,OAAO,QAAQ,GAAG;wCAClB,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;wCAC/C,aAAY;wCACZ,WAAU;;;;;;;;;;;;0CAKd,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,gBAAgB;kDAChC;;;;;;kDAGD,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,gBAAgB;kDAChC;;;;;;kDAGD,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,gBAAgB;kDAChC;;;;;;;;;;;;;;;;;;;;;;;;0BAQP,8OAAC,gIAAA,CAAA,OAAI;gBAAC,cAAa;gBAAU,WAAU;;kCACrC,8OAAC,gIAAA,CAAA,WAAQ;wBAAC,WAAU;;0CAClB,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;0CAAU;;;;;;0CAC7B,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAO,UAAU,CAAC;oCAAC;oCAAQ;oCAAO;iCAAQ,CAAC,QAAQ,CAAC,QAAQ,MAAM;0CAAG;;;;;;0CAGxF,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;0CAAO;;;;;;;;;;;;kCAG5B,8OAAC,gIAAA,CAAA,cAAW;wBAAC,OAAM;kCACjB,cAAA,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;8CACT,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAU;;;;;;0DAC/B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS,IAAM,iBAAiB;kEACjC;;;;;;kEAGD,8OAAC,kIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS,IAAM,iBAAiB;kEACjC;;;;;;;;;;;;;;;;;;;;;;;8CAMP,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;;wCAEpB,OAAO,OAAO,CAAC,QAAQ,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,iBAChD,8OAAC;gDAAc,WAAU;;kEACvB,8OAAC,iIAAA,CAAA,QAAK;wDAAC,OAAO;wDAAK,QAAQ;wDAAC,WAAU;;;;;;kEACtC,8OAAC,iIAAA,CAAA,QAAK;wDACJ,OAAO;wDACP,UAAU,CAAC,IAAM,wBAAwB,KAAK,EAAE,MAAM,CAAC,KAAK;wDAC5D,WAAU;;;;;;kEAEZ,8OAAC,kIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS,IAAM,mBAAmB;kEAElC,cAAA,8OAAC,4LAAA,CAAA,IAAC;4DAAC,WAAU;;;;;;;;;;;;+CAZP;;;;;sDAkBZ,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iIAAA,CAAA,QAAK;oDACJ,OAAO;oDACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;oDAC/C,aAAY;oDACZ,WAAU;;;;;;8DAEZ,8OAAC,iIAAA,CAAA,QAAK;oDACJ,OAAO;oDACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;oDACjD,aAAY;oDACZ,WAAU;;;;;;8DAEZ,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS;oDACT,UAAU,CAAC,gBAAgB,CAAC;8DAE5B,cAAA,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAO1B,8OAAC,gIAAA,CAAA,cAAW;wBAAC,OAAM;kCACjB,cAAA,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;8CACT,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAU;;;;;;0DAC/B,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS;;sEAET,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;;;;;;;;;;;8CAMzC,8OAAC,gIAAA,CAAA,cAAW;;sDACV,8OAAC,oIAAA,CAAA,WAAQ;4CACP,OAAO,QAAQ,IAAI,IAAI;4CACvB,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;4CAChD,aAAY;4CACZ,WAAU;;;;;;sDAIZ,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS,IAAM,iBAAiB;8DACjC;;;;;;8DAGD,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS,IAAM,iBAAiB;8DACjC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQT,8OAAC,gIAAA,CAAA,cAAW;wBAAC,OAAM;kCACjB,cAAA,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;8CACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAU;;;;;;;;;;;8CAEjC,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,8OAAC;4CAAI,WAAU;sDAAgC;;;;;;sDAI/C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS,IAAM,iBAAiB;8DACjC;;;;;;8DAGD,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS,IAAM,gBAAgB;4DAC7B,SAAS;gEACP,GAAG,QAAQ,OAAO;gEAClB,aAAa;4DACf;wDACF;8DACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjB", "debugId": null}}, {"offset": {"line": 1975, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/FlowUi/src/components/api-testing/response-viewer.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport { Button } from '@/components/ui/button'\nimport { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs'\nimport { Copy, Download, Eye, Code } from 'lucide-react'\nimport { APIResponse } from '@/types'\n\ninterface ResponseViewerProps {\n  response: APIResponse | null\n}\n\nexport function ResponseViewer({ response }: ResponseViewerProps) {\n  const [viewMode, setViewMode] = useState<'formatted' | 'raw'>('formatted')\n\n  if (!response) {\n    return (\n      <div className=\"h-full flex items-center justify-center p-8\">\n        <div className=\"text-center\">\n          <div className=\"w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4\">\n            <Eye className=\"w-8 h-8 text-gray-400\" />\n          </div>\n          <h3 className=\"text-lg font-medium mb-2\">No Response Yet</h3>\n          <p className=\"text-muted-foreground\">\n            Send a request to see the response here\n          </p>\n        </div>\n      </div>\n    )\n  }\n\n  const getStatusColor = (status: number) => {\n    if (status >= 200 && status < 300) return 'bg-green-500'\n    if (status >= 300 && status < 400) return 'bg-yellow-500'\n    if (status >= 400 && status < 500) return 'bg-orange-500'\n    if (status >= 500) return 'bg-red-500'\n    return 'bg-gray-500'\n  }\n\n  const getStatusText = (status: number) => {\n    const statusTexts: Record<number, string> = {\n      200: 'OK',\n      201: 'Created',\n      204: 'No Content',\n      400: 'Bad Request',\n      401: 'Unauthorized',\n      403: 'Forbidden',\n      404: 'Not Found',\n      500: 'Internal Server Error',\n      502: 'Bad Gateway',\n      503: 'Service Unavailable'\n    }\n    return statusTexts[status] || 'Unknown'\n  }\n\n  const copyToClipboard = (text: string) => {\n    navigator.clipboard.writeText(text).then(() => {\n      // Could add a toast notification here\n    })\n  }\n\n  const downloadResponse = () => {\n    const blob = new Blob([JSON.stringify(response.body, null, 2)], {\n      type: 'application/json'\n    })\n    const url = URL.createObjectURL(blob)\n    const a = document.createElement('a')\n    a.href = url\n    a.download = `response-${Date.now()}.json`\n    document.body.appendChild(a)\n    a.click()\n    document.body.removeChild(a)\n    URL.revokeObjectURL(url)\n  }\n\n  const formatJSON = (obj: any) => {\n    try {\n      return JSON.stringify(obj, null, 2)\n    } catch {\n      return String(obj)\n    }\n  }\n\n  const formatSize = (bytes: number) => {\n    if (bytes === 0) return '0 B'\n    const k = 1024\n    const sizes = ['B', 'KB', 'MB', 'GB']\n    const i = Math.floor(Math.log(bytes) / Math.log(k))\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]\n  }\n\n  return (\n    <div className=\"h-full overflow-auto p-4 space-y-4\">\n      {/* Response Status */}\n      <Card>\n        <CardHeader>\n          <div className=\"flex items-center justify-between\">\n            <CardTitle className=\"text-sm\">Response Status</CardTitle>\n            <div className=\"flex gap-2\">\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={() => copyToClipboard(formatJSON(response.body))}\n              >\n                <Copy className=\"w-4 h-4 mr-2\" />\n                Copy\n              </Button>\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={downloadResponse}\n              >\n                <Download className=\"w-4 h-4 mr-2\" />\n                Download\n              </Button>\n            </div>\n          </div>\n        </CardHeader>\n        <CardContent>\n          <div className=\"flex items-center gap-4\">\n            <div className=\"flex items-center gap-2\">\n              <div className={`w-3 h-3 rounded-full ${getStatusColor(response.status)}`} />\n              <Badge variant={response.status >= 200 && response.status < 300 ? \"default\" : \"destructive\"}>\n                {response.status} {getStatusText(response.status)}\n              </Badge>\n            </div>\n            <div className=\"text-sm text-muted-foreground\">\n              {response.time}ms\n            </div>\n            <div className=\"text-sm text-muted-foreground\">\n              {formatSize(response.size)}\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Response Content */}\n      <Tabs defaultValue=\"body\" className=\"space-y-4\">\n        <TabsList className=\"grid w-full grid-cols-2\">\n          <TabsTrigger value=\"body\">Response Body</TabsTrigger>\n          <TabsTrigger value=\"headers\">Headers ({Object.keys(response.headers).length})</TabsTrigger>\n        </TabsList>\n\n        <TabsContent value=\"body\">\n          <Card>\n            <CardHeader>\n              <div className=\"flex items-center justify-between\">\n                <CardTitle className=\"text-sm\">Response Body</CardTitle>\n                <div className=\"flex gap-2\">\n                  <Button\n                    variant={viewMode === 'formatted' ? 'default' : 'outline'}\n                    size=\"sm\"\n                    onClick={() => setViewMode('formatted')}\n                  >\n                    <Eye className=\"w-4 h-4 mr-2\" />\n                    Formatted\n                  </Button>\n                  <Button\n                    variant={viewMode === 'raw' ? 'default' : 'outline'}\n                    size=\"sm\"\n                    onClick={() => setViewMode('raw')}\n                  >\n                    <Code className=\"w-4 h-4 mr-2\" />\n                    Raw\n                  </Button>\n                </div>\n              </div>\n            </CardHeader>\n            <CardContent>\n              {response.status === 0 ? (\n                <div className=\"p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded\">\n                  <h4 className=\"font-medium text-red-800 dark:text-red-200 mb-2\">Network Error</h4>\n                  <p className=\"text-red-600 dark:text-red-300 text-sm\">\n                    {response.body?.error || 'Failed to connect to the server'}\n                  </p>\n                </div>\n              ) : (\n                <pre className=\"bg-gray-50 dark:bg-gray-800 p-4 rounded-lg overflow-auto text-sm font-mono max-h-96\">\n                  {viewMode === 'formatted' \n                    ? formatJSON(response.body)\n                    : typeof response.body === 'string' \n                      ? response.body \n                      : JSON.stringify(response.body)\n                  }\n                </pre>\n              )}\n            </CardContent>\n          </Card>\n        </TabsContent>\n\n        <TabsContent value=\"headers\">\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"text-sm\">Response Headers</CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-2\">\n                {Object.entries(response.headers).map(([key, value]) => (\n                  <div key={key} className=\"flex items-center gap-4 py-2 border-b border-gray-100 dark:border-gray-700 last:border-0\">\n                    <div className=\"font-medium text-sm min-w-0 flex-1\">\n                      {key}\n                    </div>\n                    <div className=\"text-sm text-muted-foreground font-mono min-w-0 flex-2\">\n                      {value}\n                    </div>\n                  </div>\n                ))}\n                \n                {Object.keys(response.headers).length === 0 && (\n                  <div className=\"text-center py-4 text-muted-foreground\">\n                    No headers received\n                  </div>\n                )}\n              </div>\n            </CardContent>\n          </Card>\n        </TabsContent>\n      </Tabs>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAPA;;;;;;;;AAcO,SAAS,eAAe,EAAE,QAAQ,EAAuB;IAC9D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB;IAE9D,IAAI,CAAC,UAAU;QACb,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,gMAAA,CAAA,MAAG;4BAAC,WAAU;;;;;;;;;;;kCAEjB,8OAAC;wBAAG,WAAU;kCAA2B;;;;;;kCACzC,8OAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;;;;;;IAM7C;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,UAAU,OAAO,SAAS,KAAK,OAAO;QAC1C,IAAI,UAAU,OAAO,SAAS,KAAK,OAAO;QAC1C,IAAI,UAAU,OAAO,SAAS,KAAK,OAAO;QAC1C,IAAI,UAAU,KAAK,OAAO;QAC1B,OAAO;IACT;IAEA,MAAM,gBAAgB,CAAC;QACrB,MAAM,cAAsC;YAC1C,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;QACP;QACA,OAAO,WAAW,CAAC,OAAO,IAAI;IAChC;IAEA,MAAM,kBAAkB,CAAC;QACvB,UAAU,SAAS,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC;QACvC,sCAAsC;QACxC;IACF;IAEA,MAAM,mBAAmB;QACvB,MAAM,OAAO,IAAI,KAAK;YAAC,KAAK,SAAS,CAAC,SAAS,IAAI,EAAE,MAAM;SAAG,EAAE;YAC9D,MAAM;QACR;QACA,MAAM,MAAM,IAAI,eAAe,CAAC;QAChC,MAAM,IAAI,SAAS,aAAa,CAAC;QACjC,EAAE,IAAI,GAAG;QACT,EAAE,QAAQ,GAAG,CAAC,SAAS,EAAE,KAAK,GAAG,GAAG,KAAK,CAAC;QAC1C,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,EAAE,KAAK;QACP,SAAS,IAAI,CAAC,WAAW,CAAC;QAC1B,IAAI,eAAe,CAAC;IACtB;IAEA,MAAM,aAAa,CAAC;QAClB,IAAI;YACF,OAAO,KAAK,SAAS,CAAC,KAAK,MAAM;QACnC,EAAE,OAAM;YACN,OAAO,OAAO;QAChB;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,IAAI,UAAU,GAAG,OAAO;QACxB,MAAM,IAAI;QACV,MAAM,QAAQ;YAAC;YAAK;YAAM;YAAM;SAAK;QACrC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;QAChD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;IACzE;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;8CAAU;;;;;;8CAC/B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS,IAAM,gBAAgB,WAAW,SAAS,IAAI;;8DAEvD,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGnC,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS;;8DAET,8OAAC,0MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;;;;;;kCAM7C,8OAAC,gIAAA,CAAA,cAAW;kCACV,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAW,CAAC,qBAAqB,EAAE,eAAe,SAAS,MAAM,GAAG;;;;;;sDACzE,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAS,SAAS,MAAM,IAAI,OAAO,SAAS,MAAM,GAAG,MAAM,YAAY;;gDAC3E,SAAS,MAAM;gDAAC;gDAAE,cAAc,SAAS,MAAM;;;;;;;;;;;;;8CAGpD,8OAAC;oCAAI,WAAU;;wCACZ,SAAS,IAAI;wCAAC;;;;;;;8CAEjB,8OAAC;oCAAI,WAAU;8CACZ,WAAW,SAAS,IAAI;;;;;;;;;;;;;;;;;;;;;;;0BAOjC,8OAAC,gIAAA,CAAA,OAAI;gBAAC,cAAa;gBAAO,WAAU;;kCAClC,8OAAC,gIAAA,CAAA,WAAQ;wBAAC,WAAU;;0CAClB,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;0CAAO;;;;;;0CAC1B,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;;oCAAU;oCAAU,OAAO,IAAI,CAAC,SAAS,OAAO,EAAE,MAAM;oCAAC;;;;;;;;;;;;;kCAG9E,8OAAC,gIAAA,CAAA,cAAW;wBAAC,OAAM;kCACjB,cAAA,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;8CACT,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAU;;;;;;0DAC/B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kIAAA,CAAA,SAAM;wDACL,SAAS,aAAa,cAAc,YAAY;wDAChD,MAAK;wDACL,SAAS,IAAM,YAAY;;0EAE3B,8OAAC,gMAAA,CAAA,MAAG;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;kEAGlC,8OAAC,kIAAA,CAAA,SAAM;wDACL,SAAS,aAAa,QAAQ,YAAY;wDAC1C,MAAK;wDACL,SAAS,IAAM,YAAY;;0EAE3B,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;;;;;;;;;;;;;;;;;;8CAMzC,8OAAC,gIAAA,CAAA,cAAW;8CACT,SAAS,MAAM,KAAK,kBACnB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAkD;;;;;;0DAChE,8OAAC;gDAAE,WAAU;0DACV,SAAS,IAAI,EAAE,SAAS;;;;;;;;;;;6DAI7B,8OAAC;wCAAI,WAAU;kDACZ,aAAa,cACV,WAAW,SAAS,IAAI,IACxB,OAAO,SAAS,IAAI,KAAK,WACvB,SAAS,IAAI,GACb,KAAK,SAAS,CAAC,SAAS,IAAI;;;;;;;;;;;;;;;;;;;;;;kCAQ5C,8OAAC,gIAAA,CAAA,cAAW;wBAAC,OAAM;kCACjB,cAAA,8OAAC,gIAAA,CAAA,OAAI;;8CACH,8OAAC,gIAAA,CAAA,aAAU;8CACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAU;;;;;;;;;;;8CAEjC,8OAAC,gIAAA,CAAA,cAAW;8CACV,cAAA,8OAAC;wCAAI,WAAU;;4CACZ,OAAO,OAAO,CAAC,SAAS,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,iBACjD,8OAAC;oDAAc,WAAU;;sEACvB,8OAAC;4DAAI,WAAU;sEACZ;;;;;;sEAEH,8OAAC;4DAAI,WAAU;sEACZ;;;;;;;mDALK;;;;;4CAUX,OAAO,IAAI,CAAC,SAAS,OAAO,EAAE,MAAM,KAAK,mBACxC,8OAAC;gDAAI,WAAU;0DAAyC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW1E", "debugId": null}}, {"offset": {"line": 2500, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/FlowUi/src/components/api-testing/request-history.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { \n  Clock, \n  Search, \n  Filter, \n  Trash2, \n  RotateCcw,\n  CheckCircle,\n  XCircle,\n  AlertCircle\n} from 'lucide-react'\n\ninterface HistoryItem {\n  id: string\n  method: string\n  url: string\n  status: number\n  duration: number\n  timestamp: Date\n  success: boolean\n}\n\nexport function RequestHistory() {\n  const [searchTerm, setSearchTerm] = useState('')\n  const [filterStatus, setFilterStatus] = useState<'all' | 'success' | 'error'>('all')\n\n  // Mock history data - in a real app, this would come from the store\n  const historyItems: HistoryItem[] = [\n    {\n      id: '1',\n      method: 'GET',\n      url: 'https://jsonplaceholder.typicode.com/posts',\n      status: 200,\n      duration: 245,\n      timestamp: new Date(Date.now() - 1000 * 60 * 5), // 5 minutes ago\n      success: true\n    },\n    {\n      id: '2',\n      method: 'POST',\n      url: 'https://jsonplaceholder.typicode.com/posts',\n      status: 201,\n      duration: 312,\n      timestamp: new Date(Date.now() - 1000 * 60 * 15), // 15 minutes ago\n      success: true\n    },\n    {\n      id: '3',\n      method: 'GET',\n      url: 'https://api.example.com/users',\n      status: 404,\n      duration: 156,\n      timestamp: new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago\n      success: false\n    },\n    {\n      id: '4',\n      method: 'POST',\n      url: 'https://your-n8n-instance.com/webhook/test',\n      status: 0,\n      duration: 5000,\n      timestamp: new Date(Date.now() - 1000 * 60 * 60), // 1 hour ago\n      success: false\n    }\n  ]\n\n  const getStatusIcon = (status: number, success: boolean) => {\n    if (!success || status === 0) {\n      return <XCircle className=\"w-4 h-4 text-red-500\" />\n    }\n    if (status >= 200 && status < 300) {\n      return <CheckCircle className=\"w-4 h-4 text-green-500\" />\n    }\n    if (status >= 300 && status < 400) {\n      return <AlertCircle className=\"w-4 h-4 text-yellow-500\" />\n    }\n    return <XCircle className=\"w-4 h-4 text-red-500\" />\n  }\n\n  const getMethodColor = (method: string) => {\n    const colors: Record<string, string> = {\n      'GET': 'bg-green-500',\n      'POST': 'bg-blue-500',\n      'PUT': 'bg-yellow-500',\n      'DELETE': 'bg-red-500',\n      'PATCH': 'bg-purple-500'\n    }\n    return colors[method] || 'bg-gray-500'\n  }\n\n  const formatTimestamp = (timestamp: Date) => {\n    const now = new Date()\n    const diff = now.getTime() - timestamp.getTime()\n    const minutes = Math.floor(diff / (1000 * 60))\n    const hours = Math.floor(diff / (1000 * 60 * 60))\n    const days = Math.floor(diff / (1000 * 60 * 60 * 24))\n\n    if (minutes < 1) return 'Just now'\n    if (minutes < 60) return `${minutes}m ago`\n    if (hours < 24) return `${hours}h ago`\n    return `${days}d ago`\n  }\n\n  const filteredHistory = historyItems.filter(item => {\n    const matchesSearch = item.url.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                         item.method.toLowerCase().includes(searchTerm.toLowerCase())\n    \n    const matchesFilter = filterStatus === 'all' ||\n                         (filterStatus === 'success' && item.success) ||\n                         (filterStatus === 'error' && !item.success)\n    \n    return matchesSearch && matchesFilter\n  })\n\n  const handleRerunRequest = (item: HistoryItem) => {\n    // In a real implementation, this would recreate the request and run it\n    console.log('Rerunning request:', item)\n  }\n\n  const clearHistory = () => {\n    if (confirm('Are you sure you want to clear all history?')) {\n      // In a real implementation, this would clear the history from the store\n      console.log('Clearing history')\n    }\n  }\n\n  return (\n    <div className=\"h-full overflow-auto p-4 space-y-4\">\n      {/* Header */}\n      <Card>\n        <CardHeader>\n          <div className=\"flex items-center justify-between\">\n            <CardTitle className=\"text-sm flex items-center gap-2\">\n              <Clock className=\"w-4 h-4\" />\n              Request History\n            </CardTitle>\n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              onClick={clearHistory}\n              className=\"text-red-600 hover:text-red-700\"\n            >\n              <Trash2 className=\"w-4 h-4 mr-2\" />\n              Clear All\n            </Button>\n          </div>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          {/* Search and Filter */}\n          <div className=\"flex gap-2\">\n            <div className=\"relative flex-1\">\n              <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400\" />\n              <Input\n                value={searchTerm}\n                onChange={(e) => setSearchTerm(e.target.value)}\n                placeholder=\"Search requests...\"\n                className=\"pl-10\"\n              />\n            </div>\n            <div className=\"flex gap-1\">\n              <Button\n                variant={filterStatus === 'all' ? 'default' : 'outline'}\n                size=\"sm\"\n                onClick={() => setFilterStatus('all')}\n              >\n                All\n              </Button>\n              <Button\n                variant={filterStatus === 'success' ? 'default' : 'outline'}\n                size=\"sm\"\n                onClick={() => setFilterStatus('success')}\n              >\n                Success\n              </Button>\n              <Button\n                variant={filterStatus === 'error' ? 'default' : 'outline'}\n                size=\"sm\"\n                onClick={() => setFilterStatus('error')}\n              >\n                Error\n              </Button>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* History Items */}\n      <div className=\"space-y-2\">\n        {filteredHistory.length === 0 ? (\n          <Card>\n            <CardContent className=\"py-8\">\n              <div className=\"text-center\">\n                <Clock className=\"w-12 h-12 text-gray-400 mx-auto mb-4\" />\n                <h3 className=\"text-lg font-medium mb-2\">No History Found</h3>\n                <p className=\"text-muted-foreground\">\n                  {searchTerm || filterStatus !== 'all' \n                    ? 'No requests match your search criteria'\n                    : 'Send some requests to see them here'\n                  }\n                </p>\n              </div>\n            </CardContent>\n          </Card>\n        ) : (\n          filteredHistory.map((item) => (\n            <Card key={item.id} className=\"hover:shadow-md transition-shadow\">\n              <CardContent className=\"p-4\">\n                <div className=\"flex items-center justify-between\">\n                  <div className=\"flex items-center gap-3 flex-1 min-w-0\">\n                    {getStatusIcon(item.status, item.success)}\n                    \n                    <div className=\"flex items-center gap-2\">\n                      <div className={`w-2 h-2 rounded-full ${getMethodColor(item.method)}`} />\n                      <Badge variant=\"outline\" className=\"text-xs\">\n                        {item.method}\n                      </Badge>\n                    </div>\n                    \n                    <div className=\"flex-1 min-w-0\">\n                      <div className=\"text-sm font-medium truncate\">\n                        {item.url}\n                      </div>\n                      <div className=\"text-xs text-muted-foreground\">\n                        {formatTimestamp(item.timestamp)} • {item.duration}ms\n                      </div>\n                    </div>\n                  </div>\n                  \n                  <div className=\"flex items-center gap-2\">\n                    <Badge \n                      variant={item.success ? \"default\" : \"destructive\"}\n                      className=\"text-xs\"\n                    >\n                      {item.status === 0 ? 'Network Error' : item.status}\n                    </Badge>\n                    \n                    <Button\n                      variant=\"ghost\"\n                      size=\"sm\"\n                      onClick={() => handleRerunRequest(item)}\n                      className=\"h-8 w-8 p-0\"\n                    >\n                      <RotateCcw className=\"w-3 h-3\" />\n                    </Button>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n          ))\n        )}\n      </div>\n\n      {/* Stats */}\n      {filteredHistory.length > 0 && (\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"text-sm\">Statistics</CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"grid grid-cols-3 gap-4 text-center\">\n              <div>\n                <div className=\"text-2xl font-bold text-green-600\">\n                  {filteredHistory.filter(item => item.success).length}\n                </div>\n                <div className=\"text-xs text-muted-foreground\">Successful</div>\n              </div>\n              <div>\n                <div className=\"text-2xl font-bold text-red-600\">\n                  {filteredHistory.filter(item => !item.success).length}\n                </div>\n                <div className=\"text-xs text-muted-foreground\">Failed</div>\n              </div>\n              <div>\n                <div className=\"text-2xl font-bold text-blue-600\">\n                  {Math.round(filteredHistory.reduce((acc, item) => acc + item.duration, 0) / filteredHistory.length)}ms\n                </div>\n                <div className=\"text-xs text-muted-foreground\">Avg Time</div>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAPA;;;;;;;;AA4BO,SAAS;IACd,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA+B;IAE9E,oEAAoE;IACpE,MAAM,eAA8B;QAClC;YACE,IAAI;YACJ,QAAQ;YACR,KAAK;YACL,QAAQ;YACR,UAAU;YACV,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,OAAO,KAAK;YAC7C,SAAS;QACX;QACA;YACE,IAAI;YACJ,QAAQ;YACR,KAAK;YACL,QAAQ;YACR,UAAU;YACV,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,OAAO,KAAK;YAC7C,SAAS;QACX;QACA;YACE,IAAI;YACJ,QAAQ;YACR,KAAK;YACL,QAAQ;YACR,UAAU;YACV,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,OAAO,KAAK;YAC7C,SAAS;QACX;QACA;YACE,IAAI;YACJ,QAAQ;YACR,KAAK;YACL,QAAQ;YACR,UAAU;YACV,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,OAAO,KAAK;YAC7C,SAAS;QACX;KACD;IAED,MAAM,gBAAgB,CAAC,QAAgB;QACrC,IAAI,CAAC,WAAW,WAAW,GAAG;YAC5B,qBAAO,8OAAC,4MAAA,CAAA,UAAO;gBAAC,WAAU;;;;;;QAC5B;QACA,IAAI,UAAU,OAAO,SAAS,KAAK;YACjC,qBAAO,8OAAC,2NAAA,CAAA,cAAW;gBAAC,WAAU;;;;;;QAChC;QACA,IAAI,UAAU,OAAO,SAAS,KAAK;YACjC,qBAAO,8OAAC,oNAAA,CAAA,cAAW;gBAAC,WAAU;;;;;;QAChC;QACA,qBAAO,8OAAC,4MAAA,CAAA,UAAO;YAAC,WAAU;;;;;;IAC5B;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,SAAiC;YACrC,OAAO;YACP,QAAQ;YACR,OAAO;YACP,UAAU;YACV,SAAS;QACX;QACA,OAAO,MAAM,CAAC,OAAO,IAAI;IAC3B;IAEA,MAAM,kBAAkB,CAAC;QACvB,MAAM,MAAM,IAAI;QAChB,MAAM,OAAO,IAAI,OAAO,KAAK,UAAU,OAAO;QAC9C,MAAM,UAAU,KAAK,KAAK,CAAC,OAAO,CAAC,OAAO,EAAE;QAC5C,MAAM,QAAQ,KAAK,KAAK,CAAC,OAAO,CAAC,OAAO,KAAK,EAAE;QAC/C,MAAM,OAAO,KAAK,KAAK,CAAC,OAAO,CAAC,OAAO,KAAK,KAAK,EAAE;QAEnD,IAAI,UAAU,GAAG,OAAO;QACxB,IAAI,UAAU,IAAI,OAAO,GAAG,QAAQ,KAAK,CAAC;QAC1C,IAAI,QAAQ,IAAI,OAAO,GAAG,MAAM,KAAK,CAAC;QACtC,OAAO,GAAG,KAAK,KAAK,CAAC;IACvB;IAEA,MAAM,kBAAkB,aAAa,MAAM,CAAC,CAAA;QAC1C,MAAM,gBAAgB,KAAK,GAAG,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACvD,KAAK,MAAM,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QAE9E,MAAM,gBAAgB,iBAAiB,SACjB,iBAAiB,aAAa,KAAK,OAAO,IAC1C,iBAAiB,WAAW,CAAC,KAAK,OAAO;QAE/D,OAAO,iBAAiB;IAC1B;IAEA,MAAM,qBAAqB,CAAC;QAC1B,uEAAuE;QACvE,QAAQ,GAAG,CAAC,sBAAsB;IACpC;IAEA,MAAM,eAAe;QACnB,IAAI,QAAQ,gDAAgD;YAC1D,wEAAwE;YACxE,QAAQ,GAAG,CAAC;QACd;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;wCAAY;;;;;;;8CAG/B,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS;oCACT,WAAU;;sDAEV,8OAAC,0MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;kCAKzC,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;kCAErB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,8OAAC,iIAAA,CAAA,QAAK;4CACJ,OAAO;4CACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4CAC7C,aAAY;4CACZ,WAAU;;;;;;;;;;;;8CAGd,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAS,iBAAiB,QAAQ,YAAY;4CAC9C,MAAK;4CACL,SAAS,IAAM,gBAAgB;sDAChC;;;;;;sDAGD,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAS,iBAAiB,YAAY,YAAY;4CAClD,MAAK;4CACL,SAAS,IAAM,gBAAgB;sDAChC;;;;;;sDAGD,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAS,iBAAiB,UAAU,YAAY;4CAChD,MAAK;4CACL,SAAS,IAAM,gBAAgB;sDAChC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAST,8OAAC;gBAAI,WAAU;0BACZ,gBAAgB,MAAM,KAAK,kBAC1B,8OAAC,gIAAA,CAAA,OAAI;8BACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;kCACrB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;8CACjB,8OAAC;oCAAG,WAAU;8CAA2B;;;;;;8CACzC,8OAAC;oCAAE,WAAU;8CACV,cAAc,iBAAiB,QAC5B,2CACA;;;;;;;;;;;;;;;;;;;;;2BAOZ,gBAAgB,GAAG,CAAC,CAAC,qBACnB,8OAAC,gIAAA,CAAA,OAAI;wBAAe,WAAU;kCAC5B,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;4CACZ,cAAc,KAAK,MAAM,EAAE,KAAK,OAAO;0DAExC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAW,CAAC,qBAAqB,EAAE,eAAe,KAAK,MAAM,GAAG;;;;;;kEACrE,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAU,WAAU;kEAChC,KAAK,MAAM;;;;;;;;;;;;0DAIhB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACZ,KAAK,GAAG;;;;;;kEAEX,8OAAC;wDAAI,WAAU;;4DACZ,gBAAgB,KAAK,SAAS;4DAAE;4DAAI,KAAK,QAAQ;4DAAC;;;;;;;;;;;;;;;;;;;kDAKzD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;gDACJ,SAAS,KAAK,OAAO,GAAG,YAAY;gDACpC,WAAU;0DAET,KAAK,MAAM,KAAK,IAAI,kBAAkB,KAAK,MAAM;;;;;;0DAGpD,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS,IAAM,mBAAmB;gDAClC,WAAU;0DAEV,cAAA,8OAAC,gNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;uBArCpB,KAAK,EAAE;;;;;;;;;;YAgDvB,gBAAgB,MAAM,GAAG,mBACxB,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;sCAAU;;;;;;;;;;;kCAEjC,8OAAC,gIAAA,CAAA,cAAW;kCACV,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAI,WAAU;sDACZ,gBAAgB,MAAM,CAAC,CAAA,OAAQ,KAAK,OAAO,EAAE,MAAM;;;;;;sDAEtD,8OAAC;4CAAI,WAAU;sDAAgC;;;;;;;;;;;;8CAEjD,8OAAC;;sDACC,8OAAC;4CAAI,WAAU;sDACZ,gBAAgB,MAAM,CAAC,CAAA,OAAQ,CAAC,KAAK,OAAO,EAAE,MAAM;;;;;;sDAEvD,8OAAC;4CAAI,WAAU;sDAAgC;;;;;;;;;;;;8CAEjD,8OAAC;;sDACC,8OAAC;4CAAI,WAAU;;gDACZ,KAAK,KAAK,CAAC,gBAAgB,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,QAAQ,EAAE,KAAK,gBAAgB,MAAM;gDAAE;;;;;;;sDAEtG,8OAAC;4CAAI,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ/D", "debugId": null}}, {"offset": {"line": 3079, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/FlowUi/src/components/api-testing/collection-manager.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport { \n  Plus, \n  Folder, \n  FileText, \n  MoreHorizontal, \n  Trash2, \n  Edit,\n  Play\n} from 'lucide-react'\nimport { useAPITestingStore } from '@/lib/store'\nimport { generateId } from '@/lib/utils'\n\nexport function CollectionManager() {\n  const { \n    requests, \n    currentRequest, \n    setCurrentRequest, \n    addRequest, \n    removeRequest \n  } = useAPITestingStore()\n\n  const [isCreatingCollection, setIsCreatingCollection] = useState(false)\n  const [newCollectionName, setNewCollectionName] = useState('')\n\n  const handleCreateCollection = () => {\n    if (newCollectionName.trim()) {\n      // For now, we'll just create a new request in this \"collection\"\n      // In a full implementation, you'd create actual collections\n      setNewCollectionName('')\n      setIsCreatingCollection(false)\n    }\n  }\n\n  const handleSelectRequest = (request: any) => {\n    setCurrentRequest(request)\n  }\n\n  const handleDeleteRequest = (requestId: string, e: React.MouseEvent) => {\n    e.stopPropagation()\n    if (confirm('Are you sure you want to delete this request?')) {\n      removeRequest(requestId)\n      if (currentRequest?.id === requestId) {\n        setCurrentRequest(null)\n      }\n    }\n  }\n\n  const getMethodColor = (method: string) => {\n    const colors: Record<string, string> = {\n      'GET': 'bg-green-500',\n      'POST': 'bg-blue-500',\n      'PUT': 'bg-yellow-500',\n      'DELETE': 'bg-red-500',\n      'PATCH': 'bg-purple-500',\n      'HEAD': 'bg-gray-500',\n      'OPTIONS': 'bg-gray-500'\n    }\n    return colors[method] || 'bg-gray-500'\n  }\n\n  const sampleRequests = [\n    {\n      id: 'sample-1',\n      name: 'Get Posts',\n      method: 'GET',\n      url: 'https://jsonplaceholder.typicode.com/posts',\n      headers: {},\n      body: '',\n      collection_id: 'samples',\n      created_at: new Date().toISOString(),\n      updated_at: new Date().toISOString()\n    },\n    {\n      id: 'sample-2',\n      name: 'Create Post',\n      method: 'POST',\n      url: 'https://jsonplaceholder.typicode.com/posts',\n      headers: { 'Content-Type': 'application/json' },\n      body: '{\\n  \"title\": \"Sample Post\",\\n  \"body\": \"This is a sample post\",\\n  \"userId\": 1\\n}',\n      collection_id: 'samples',\n      created_at: new Date().toISOString(),\n      updated_at: new Date().toISOString()\n    },\n    {\n      id: 'sample-3',\n      name: 'N8N Webhook Test',\n      method: 'POST',\n      url: 'https://your-n8n-instance.com/webhook/test',\n      headers: { 'Content-Type': 'application/json' },\n      body: '{\\n  \"message\": \"Hello from FlowUI\",\\n  \"timestamp\": \"2024-01-01T00:00:00Z\"\\n}',\n      collection_id: 'n8n',\n      created_at: new Date().toISOString(),\n      updated_at: new Date().toISOString()\n    }\n  ]\n\n  const allRequests = [...requests, ...sampleRequests]\n\n  return (\n    <div className=\"p-4 space-y-4\">\n      <div className=\"flex items-center justify-between\">\n        <h2 className=\"text-lg font-semibold\">Collections</h2>\n        <Button\n          variant=\"ghost\"\n          size=\"sm\"\n          onClick={() => setIsCreatingCollection(true)}\n        >\n          <Plus className=\"w-4 h-4\" />\n        </Button>\n      </div>\n\n      {/* Sample Collections */}\n      <div className=\"space-y-2\">\n        <Card>\n          <CardHeader className=\"pb-2\">\n            <div className=\"flex items-center gap-2\">\n              <Folder className=\"w-4 h-4 text-blue-500\" />\n              <CardTitle className=\"text-sm\">Sample Requests</CardTitle>\n              <Badge variant=\"secondary\" className=\"text-xs\">3</Badge>\n            </div>\n          </CardHeader>\n          <CardContent className=\"pt-0 space-y-1\">\n            {sampleRequests.filter(req => req.collection_id === 'samples').map((request) => (\n              <div\n                key={request.id}\n                onClick={() => handleSelectRequest(request)}\n                className={`\n                  flex items-center gap-2 p-2 rounded cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700\n                  ${currentRequest?.id === request.id ? 'bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800' : ''}\n                `}\n              >\n                <div className={`w-2 h-2 rounded-full ${getMethodColor(request.method)}`} />\n                <FileText className=\"w-3 h-3 text-gray-400\" />\n                <span className=\"text-sm flex-1 truncate\">{request.name}</span>\n                <Badge variant=\"outline\" className=\"text-xs\">\n                  {request.method}\n                </Badge>\n              </div>\n            ))}\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardHeader className=\"pb-2\">\n            <div className=\"flex items-center gap-2\">\n              <Folder className=\"w-4 h-4 text-green-500\" />\n              <CardTitle className=\"text-sm\">N8N Webhooks</CardTitle>\n              <Badge variant=\"secondary\" className=\"text-xs\">1</Badge>\n            </div>\n          </CardHeader>\n          <CardContent className=\"pt-0 space-y-1\">\n            {sampleRequests.filter(req => req.collection_id === 'n8n').map((request) => (\n              <div\n                key={request.id}\n                onClick={() => handleSelectRequest(request)}\n                className={`\n                  flex items-center gap-2 p-2 rounded cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700\n                  ${currentRequest?.id === request.id ? 'bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800' : ''}\n                `}\n              >\n                <div className={`w-2 h-2 rounded-full ${getMethodColor(request.method)}`} />\n                <FileText className=\"w-3 h-3 text-gray-400\" />\n                <span className=\"text-sm flex-1 truncate\">{request.name}</span>\n                <Badge variant=\"outline\" className=\"text-xs\">\n                  {request.method}\n                </Badge>\n              </div>\n            ))}\n          </CardContent>\n        </Card>\n\n        {/* User's Custom Requests */}\n        {requests.length > 0 && (\n          <Card>\n            <CardHeader className=\"pb-2\">\n              <div className=\"flex items-center gap-2\">\n                <Folder className=\"w-4 h-4 text-purple-500\" />\n                <CardTitle className=\"text-sm\">My Requests</CardTitle>\n                <Badge variant=\"secondary\" className=\"text-xs\">{requests.length}</Badge>\n              </div>\n            </CardHeader>\n            <CardContent className=\"pt-0 space-y-1\">\n              {requests.map((request) => (\n                <div\n                  key={request.id}\n                  onClick={() => handleSelectRequest(request)}\n                  className={`\n                    flex items-center gap-2 p-2 rounded cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 group\n                    ${currentRequest?.id === request.id ? 'bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800' : ''}\n                  `}\n                >\n                  <div className={`w-2 h-2 rounded-full ${getMethodColor(request.method)}`} />\n                  <FileText className=\"w-3 h-3 text-gray-400\" />\n                  <span className=\"text-sm flex-1 truncate\">{request.name}</span>\n                  <Badge variant=\"outline\" className=\"text-xs\">\n                    {request.method}\n                  </Badge>\n                  <Button\n                    variant=\"ghost\"\n                    size=\"sm\"\n                    className=\"opacity-0 group-hover:opacity-100 h-6 w-6 p-0\"\n                    onClick={(e) => handleDeleteRequest(request.id, e)}\n                  >\n                    <Trash2 className=\"w-3 h-3\" />\n                  </Button>\n                </div>\n              ))}\n            </CardContent>\n          </Card>\n        )}\n      </div>\n\n      {/* Create Collection Form */}\n      {isCreatingCollection && (\n        <Card>\n          <CardContent className=\"pt-4\">\n            <div className=\"space-y-2\">\n              <Input\n                value={newCollectionName}\n                onChange={(e) => setNewCollectionName(e.target.value)}\n                placeholder=\"Collection name\"\n                autoFocus\n              />\n              <div className=\"flex gap-2\">\n                <Button\n                  size=\"sm\"\n                  onClick={handleCreateCollection}\n                  disabled={!newCollectionName.trim()}\n                >\n                  Create\n                </Button>\n                <Button\n                  variant=\"ghost\"\n                  size=\"sm\"\n                  onClick={() => {\n                    setIsCreatingCollection(false)\n                    setNewCollectionName('')\n                  }}\n                >\n                  Cancel\n                </Button>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n      )}\n\n      {/* Quick Actions */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"text-sm\">Quick Actions</CardTitle>\n        </CardHeader>\n        <CardContent className=\"space-y-2\">\n          <Button\n            variant=\"outline\"\n            size=\"sm\"\n            className=\"w-full justify-start\"\n            onClick={() => handleSelectRequest(sampleRequests[0])}\n          >\n            <Play className=\"w-4 h-4 mr-2\" />\n            Try Sample GET\n          </Button>\n          <Button\n            variant=\"outline\"\n            size=\"sm\"\n            className=\"w-full justify-start\"\n            onClick={() => handleSelectRequest(sampleRequests[1])}\n          >\n            <Play className=\"w-4 h-4 mr-2\" />\n            Try Sample POST\n          </Button>\n          <Button\n            variant=\"outline\"\n            size=\"sm\"\n            className=\"w-full justify-start\"\n            onClick={() => handleSelectRequest(sampleRequests[2])}\n          >\n            <Play className=\"w-4 h-4 mr-2\" />\n            Test N8N Webhook\n          </Button>\n        </CardContent>\n      </Card>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AASA;AAhBA;;;;;;;;;AAmBO,SAAS;IACd,MAAM,EACJ,QAAQ,EACR,cAAc,EACd,iBAAiB,EACjB,UAAU,EACV,aAAa,EACd,GAAG,CAAA,GAAA,mHAAA,CAAA,qBAAkB,AAAD;IAErB,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,MAAM,yBAAyB;QAC7B,IAAI,kBAAkB,IAAI,IAAI;YAC5B,gEAAgE;YAChE,4DAA4D;YAC5D,qBAAqB;YACrB,wBAAwB;QAC1B;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,kBAAkB;IACpB;IAEA,MAAM,sBAAsB,CAAC,WAAmB;QAC9C,EAAE,eAAe;QACjB,IAAI,QAAQ,kDAAkD;YAC5D,cAAc;YACd,IAAI,gBAAgB,OAAO,WAAW;gBACpC,kBAAkB;YACpB;QACF;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,SAAiC;YACrC,OAAO;YACP,QAAQ;YACR,OAAO;YACP,UAAU;YACV,SAAS;YACT,QAAQ;YACR,WAAW;QACb;QACA,OAAO,MAAM,CAAC,OAAO,IAAI;IAC3B;IAEA,MAAM,iBAAiB;QACrB;YACE,IAAI;YACJ,MAAM;YACN,QAAQ;YACR,KAAK;YACL,SAAS,CAAC;YACV,MAAM;YACN,eAAe;YACf,YAAY,IAAI,OAAO,WAAW;YAClC,YAAY,IAAI,OAAO,WAAW;QACpC;QACA;YACE,IAAI;YACJ,MAAM;YACN,QAAQ;YACR,KAAK;YACL,SAAS;gBAAE,gBAAgB;YAAmB;YAC9C,MAAM;YACN,eAAe;YACf,YAAY,IAAI,OAAO,WAAW;YAClC,YAAY,IAAI,OAAO,WAAW;QACpC;QACA;YACE,IAAI;YACJ,MAAM;YACN,QAAQ;YACR,KAAK;YACL,SAAS;gBAAE,gBAAgB;YAAmB;YAC9C,MAAM;YACN,eAAe;YACf,YAAY,IAAI,OAAO,WAAW;YAClC,YAAY,IAAI,OAAO,WAAW;QACpC;KACD;IAED,MAAM,cAAc;WAAI;WAAa;KAAe;IAEpD,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAwB;;;;;;kCACtC,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS,IAAM,wBAAwB;kCAEvC,cAAA,8OAAC,kMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;;;;;;;;;;;;0BAKpB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;gCAAC,WAAU;0CACpB,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAU;;;;;;sDAC/B,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAY,WAAU;sDAAU;;;;;;;;;;;;;;;;;0CAGnD,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;0CACpB,eAAe,MAAM,CAAC,CAAA,MAAO,IAAI,aAAa,KAAK,WAAW,GAAG,CAAC,CAAC,wBAClE,8OAAC;wCAEC,SAAS,IAAM,oBAAoB;wCACnC,WAAW,CAAC;;kBAEV,EAAE,gBAAgB,OAAO,QAAQ,EAAE,GAAG,+EAA+E,GAAG;gBAC1H,CAAC;;0DAED,8OAAC;gDAAI,WAAW,CAAC,qBAAqB,EAAE,eAAe,QAAQ,MAAM,GAAG;;;;;;0DACxE,8OAAC,8MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,8OAAC;gDAAK,WAAU;0DAA2B,QAAQ,IAAI;;;;;;0DACvD,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAU,WAAU;0DAChC,QAAQ,MAAM;;;;;;;uCAXZ,QAAQ,EAAE;;;;;;;;;;;;;;;;kCAkBvB,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;gCAAC,WAAU;0CACpB,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAU;;;;;;sDAC/B,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAY,WAAU;sDAAU;;;;;;;;;;;;;;;;;0CAGnD,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;0CACpB,eAAe,MAAM,CAAC,CAAA,MAAO,IAAI,aAAa,KAAK,OAAO,GAAG,CAAC,CAAC,wBAC9D,8OAAC;wCAEC,SAAS,IAAM,oBAAoB;wCACnC,WAAW,CAAC;;kBAEV,EAAE,gBAAgB,OAAO,QAAQ,EAAE,GAAG,+EAA+E,GAAG;gBAC1H,CAAC;;0DAED,8OAAC;gDAAI,WAAW,CAAC,qBAAqB,EAAE,eAAe,QAAQ,MAAM,GAAG;;;;;;0DACxE,8OAAC,8MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,8OAAC;gDAAK,WAAU;0DAA2B,QAAQ,IAAI;;;;;;0DACvD,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAU,WAAU;0DAChC,QAAQ,MAAM;;;;;;;uCAXZ,QAAQ,EAAE;;;;;;;;;;;;;;;;oBAmBtB,SAAS,MAAM,GAAG,mBACjB,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;gCAAC,WAAU;0CACpB,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAU;;;;;;sDAC/B,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAY,WAAU;sDAAW,SAAS,MAAM;;;;;;;;;;;;;;;;;0CAGnE,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;0CACpB,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC;wCAEC,SAAS,IAAM,oBAAoB;wCACnC,WAAW,CAAC;;oBAEV,EAAE,gBAAgB,OAAO,QAAQ,EAAE,GAAG,+EAA+E,GAAG;kBAC1H,CAAC;;0DAED,8OAAC;gDAAI,WAAW,CAAC,qBAAqB,EAAE,eAAe,QAAQ,MAAM,GAAG;;;;;;0DACxE,8OAAC,8MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,8OAAC;gDAAK,WAAU;0DAA2B,QAAQ,IAAI;;;;;;0DACvD,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAU,WAAU;0DAChC,QAAQ,MAAM;;;;;;0DAEjB,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,WAAU;gDACV,SAAS,CAAC,IAAM,oBAAoB,QAAQ,EAAE,EAAE;0DAEhD,cAAA,8OAAC,0MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;;;;;;;uCAnBf,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;YA6B1B,sCACC,8OAAC,gIAAA,CAAA,OAAI;0BACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,iIAAA,CAAA,QAAK;gCACJ,OAAO;gCACP,UAAU,CAAC,IAAM,qBAAqB,EAAE,MAAM,CAAC,KAAK;gCACpD,aAAY;gCACZ,SAAS;;;;;;0CAEX,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCACL,MAAK;wCACL,SAAS;wCACT,UAAU,CAAC,kBAAkB,IAAI;kDAClC;;;;;;kDAGD,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;4CACP,wBAAwB;4CACxB,qBAAqB;wCACvB;kDACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUX,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;sCAAU;;;;;;;;;;;kCAEjC,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,oBAAoB,cAAc,CAAC,EAAE;;kDAEpD,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGnC,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,oBAAoB,cAAc,CAAC,EAAE;;kDAEpD,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGnC,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,oBAAoB,cAAc,CAAC,EAAE;;kDAEpD,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;AAO7C", "debugId": null}}, {"offset": {"line": 3707, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/FlowUi/src/components/api-testing/api-testing-suite.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { But<PERSON> } from '@/components/ui/button'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs'\nimport { APITestingHeader } from './api-testing-header'\nimport { RequestBuilder } from './request-builder'\nimport { ResponseViewer } from './response-viewer'\nimport { RequestHistory } from './request-history'\nimport { CollectionManager } from './collection-manager'\nimport { useAPITestingStore } from '@/lib/store'\nimport { HTTPMethod, APIRequest, APIResponse } from '@/types'\nimport { generateId } from '@/lib/utils'\n\nexport function APITestingSuite() {\n  const {\n    currentRequest,\n    setCurrentRequest,\n    addRequest,\n    updateRequest,\n    setLoading,\n    setLastResponse,\n    isLoading,\n    lastResponse\n  } = useAPITestingStore()\n\n  const [activeTab, setActiveTab] = useState('builder')\n\n  const handleSendRequest = async () => {\n    if (!currentRequest) return\n\n    setLoading(true)\n    const startTime = Date.now()\n\n    try {\n      const requestOptions: RequestInit = {\n        method: currentRequest.method,\n        headers: {\n          'Content-Type': 'application/json',\n          ...currentRequest.headers,\n        },\n      }\n\n      // Add body for methods that support it\n      if (['POST', 'PUT', 'PATCH'].includes(currentRequest.method) && currentRequest.body) {\n        requestOptions.body = currentRequest.body\n      }\n\n      const response = await fetch(currentRequest.url, requestOptions)\n      const duration = Date.now() - startTime\n      \n      let responseBody\n      const contentType = response.headers.get('content-type')\n      \n      if (contentType?.includes('application/json')) {\n        responseBody = await response.json()\n      } else {\n        responseBody = await response.text()\n      }\n\n      const apiResponse: APIResponse = {\n        status: response.status,\n        headers: Object.fromEntries(response.headers.entries()),\n        body: responseBody,\n        size: JSON.stringify(responseBody).length,\n        time: duration\n      }\n\n      setLastResponse(apiResponse)\n      setActiveTab('response')\n    } catch (error) {\n      const duration = Date.now() - startTime\n      const errorResponse: APIResponse = {\n        status: 0,\n        headers: {},\n        body: {\n          error: error instanceof Error ? error.message : 'Unknown error occurred',\n          type: 'network_error'\n        },\n        size: 0,\n        time: duration\n      }\n      setLastResponse(errorResponse)\n      setActiveTab('response')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleNewRequest = () => {\n    const newRequest: APIRequest = {\n      id: generateId(),\n      collection_id: 'default',\n      name: 'New Request',\n      method: 'GET',\n      url: '',\n      headers: {},\n      body: '',\n      created_at: new Date().toISOString(),\n      updated_at: new Date().toISOString()\n    }\n    \n    setCurrentRequest(newRequest)\n    addRequest(newRequest)\n  }\n\n  const handleSaveRequest = () => {\n    if (!currentRequest) return\n    \n    const updatedRequest = {\n      ...currentRequest,\n      updated_at: new Date().toISOString()\n    }\n    \n    updateRequest(currentRequest.id, updatedRequest)\n    setCurrentRequest(updatedRequest)\n  }\n\n  return (\n    <div className=\"h-screen bg-gray-50 dark:bg-gray-900 flex flex-col\">\n      <APITestingHeader \n        onNewRequest={handleNewRequest}\n        onSaveRequest={handleSaveRequest}\n        onSendRequest={handleSendRequest}\n        isLoading={isLoading}\n        currentRequest={currentRequest}\n      />\n      \n      <div className=\"flex-1 flex overflow-hidden\">\n        {/* Sidebar - Collections */}\n        <div className=\"w-80 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 overflow-y-auto\">\n          <CollectionManager />\n        </div>\n\n        {/* Main Content */}\n        <div className=\"flex-1 flex flex-col\">\n          {currentRequest ? (\n            <Tabs value={activeTab} onValueChange={setActiveTab} className=\"flex-1 flex flex-col\">\n              <div className=\"border-b border-gray-200 dark:border-gray-700 px-4\">\n                <TabsList className=\"grid w-full grid-cols-3\">\n                  <TabsTrigger value=\"builder\">Request</TabsTrigger>\n                  <TabsTrigger value=\"response\" disabled={!lastResponse}>\n                    Response\n                    {lastResponse && (\n                      <Badge \n                        variant={lastResponse.status >= 200 && lastResponse.status < 300 ? \"default\" : \"destructive\"}\n                        className=\"ml-2\"\n                      >\n                        {lastResponse.status}\n                      </Badge>\n                    )}\n                  </TabsTrigger>\n                  <TabsTrigger value=\"history\">History</TabsTrigger>\n                </TabsList>\n              </div>\n\n              <div className=\"flex-1 overflow-hidden\">\n                <TabsContent value=\"builder\" className=\"h-full m-0\">\n                  <RequestBuilder \n                    request={currentRequest}\n                    onUpdateRequest={(updates) => {\n                      const updatedRequest = { ...currentRequest, ...updates }\n                      setCurrentRequest(updatedRequest)\n                    }}\n                  />\n                </TabsContent>\n\n                <TabsContent value=\"response\" className=\"h-full m-0\">\n                  <ResponseViewer response={lastResponse} />\n                </TabsContent>\n\n                <TabsContent value=\"history\" className=\"h-full m-0\">\n                  <RequestHistory />\n                </TabsContent>\n              </div>\n            </Tabs>\n          ) : (\n            <div className=\"flex-1 flex items-center justify-center\">\n              <Card className=\"p-8 max-w-md\">\n                <CardHeader>\n                  <CardTitle>Welcome to API Testing</CardTitle>\n                </CardHeader>\n                <CardContent>\n                  <p className=\"text-muted-foreground mb-4\">\n                    Create your first API request to get started testing your N8N webhooks and endpoints.\n                  </p>\n                  <Button onClick={handleNewRequest} className=\"w-full\">\n                    Create New Request\n                  </Button>\n                </CardContent>\n              </Card>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAdA;;;;;;;;;;;;;;AAgBO,SAAS;IACd,MAAM,EACJ,cAAc,EACd,iBAAiB,EACjB,UAAU,EACV,aAAa,EACb,UAAU,EACV,eAAe,EACf,SAAS,EACT,YAAY,EACb,GAAG,CAAA,GAAA,mHAAA,CAAA,qBAAkB,AAAD;IAErB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,oBAAoB;QACxB,IAAI,CAAC,gBAAgB;QAErB,WAAW;QACX,MAAM,YAAY,KAAK,GAAG;QAE1B,IAAI;YACF,MAAM,iBAA8B;gBAClC,QAAQ,eAAe,MAAM;gBAC7B,SAAS;oBACP,gBAAgB;oBAChB,GAAG,eAAe,OAAO;gBAC3B;YACF;YAEA,uCAAuC;YACvC,IAAI;gBAAC;gBAAQ;gBAAO;aAAQ,CAAC,QAAQ,CAAC,eAAe,MAAM,KAAK,eAAe,IAAI,EAAE;gBACnF,eAAe,IAAI,GAAG,eAAe,IAAI;YAC3C;YAEA,MAAM,WAAW,MAAM,MAAM,eAAe,GAAG,EAAE;YACjD,MAAM,WAAW,KAAK,GAAG,KAAK;YAE9B,IAAI;YACJ,MAAM,cAAc,SAAS,OAAO,CAAC,GAAG,CAAC;YAEzC,IAAI,aAAa,SAAS,qBAAqB;gBAC7C,eAAe,MAAM,SAAS,IAAI;YACpC,OAAO;gBACL,eAAe,MAAM,SAAS,IAAI;YACpC;YAEA,MAAM,cAA2B;gBAC/B,QAAQ,SAAS,MAAM;gBACvB,SAAS,OAAO,WAAW,CAAC,SAAS,OAAO,CAAC,OAAO;gBACpD,MAAM;gBACN,MAAM,KAAK,SAAS,CAAC,cAAc,MAAM;gBACzC,MAAM;YACR;YAEA,gBAAgB;YAChB,aAAa;QACf,EAAE,OAAO,OAAO;YACd,MAAM,WAAW,KAAK,GAAG,KAAK;YAC9B,MAAM,gBAA6B;gBACjC,QAAQ;gBACR,SAAS,CAAC;gBACV,MAAM;oBACJ,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;oBAChD,MAAM;gBACR;gBACA,MAAM;gBACN,MAAM;YACR;YACA,gBAAgB;YAChB,aAAa;QACf,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,mBAAmB;QACvB,MAAM,aAAyB;YAC7B,IAAI,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD;YACb,eAAe;YACf,MAAM;YACN,QAAQ;YACR,KAAK;YACL,SAAS,CAAC;YACV,MAAM;YACN,YAAY,IAAI,OAAO,WAAW;YAClC,YAAY,IAAI,OAAO,WAAW;QACpC;QAEA,kBAAkB;QAClB,WAAW;IACb;IAEA,MAAM,oBAAoB;QACxB,IAAI,CAAC,gBAAgB;QAErB,MAAM,iBAAiB;YACrB,GAAG,cAAc;YACjB,YAAY,IAAI,OAAO,WAAW;QACpC;QAEA,cAAc,eAAe,EAAE,EAAE;QACjC,kBAAkB;IACpB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,gKAAA,CAAA,mBAAgB;gBACf,cAAc;gBACd,eAAe;gBACf,eAAe;gBACf,WAAW;gBACX,gBAAgB;;;;;;0BAGlB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,6JAAA,CAAA,oBAAiB;;;;;;;;;;kCAIpB,8OAAC;wBAAI,WAAU;kCACZ,+BACC,8OAAC,gIAAA,CAAA,OAAI;4BAAC,OAAO;4BAAW,eAAe;4BAAc,WAAU;;8CAC7D,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,gIAAA,CAAA,WAAQ;wCAAC,WAAU;;0DAClB,8OAAC,gIAAA,CAAA,cAAW;gDAAC,OAAM;0DAAU;;;;;;0DAC7B,8OAAC,gIAAA,CAAA,cAAW;gDAAC,OAAM;gDAAW,UAAU,CAAC;;oDAAc;oDAEpD,8BACC,8OAAC,iIAAA,CAAA,QAAK;wDACJ,SAAS,aAAa,MAAM,IAAI,OAAO,aAAa,MAAM,GAAG,MAAM,YAAY;wDAC/E,WAAU;kEAET,aAAa,MAAM;;;;;;;;;;;;0DAI1B,8OAAC,gIAAA,CAAA,cAAW;gDAAC,OAAM;0DAAU;;;;;;;;;;;;;;;;;8CAIjC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,gIAAA,CAAA,cAAW;4CAAC,OAAM;4CAAU,WAAU;sDACrC,cAAA,8OAAC,0JAAA,CAAA,iBAAc;gDACb,SAAS;gDACT,iBAAiB,CAAC;oDAChB,MAAM,iBAAiB;wDAAE,GAAG,cAAc;wDAAE,GAAG,OAAO;oDAAC;oDACvD,kBAAkB;gDACpB;;;;;;;;;;;sDAIJ,8OAAC,gIAAA,CAAA,cAAW;4CAAC,OAAM;4CAAW,WAAU;sDACtC,cAAA,8OAAC,0JAAA,CAAA,iBAAc;gDAAC,UAAU;;;;;;;;;;;sDAG5B,8OAAC,gIAAA,CAAA,cAAW;4CAAC,OAAM;4CAAU,WAAU;sDACrC,cAAA,8OAAC,0JAAA,CAAA,iBAAc;;;;;;;;;;;;;;;;;;;;;iDAKrB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,8OAAC,gIAAA,CAAA,aAAU;kDACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;sDAAC;;;;;;;;;;;kDAEb,8OAAC,gIAAA,CAAA,cAAW;;0DACV,8OAAC;gDAAE,WAAU;0DAA6B;;;;;;0DAG1C,8OAAC,kIAAA,CAAA,SAAM;gDAAC,SAAS;gDAAkB,WAAU;0DAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWxE", "debugId": null}}, {"offset": {"line": 4048, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/FlowUi/src/app/api-testing/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useAuth } from '@/hooks/useAuth'\nimport { Loading } from '@/components/loading'\nimport { useRouter } from 'next/navigation'\nimport { useEffect } from 'react'\nimport { APITestingSuite } from '@/components/api-testing/api-testing-suite'\n\nexport default function APITestingPage() {\n  const { user, isLoading, isAuthenticated } = useAuth()\n  const router = useRouter()\n\n  // Temporarily disable auth check for testing\n  // useEffect(() => {\n  //   if (!isLoading && !isAuthenticated) {\n  //     router.push('/auth/signin')\n  //   }\n  // }, [isLoading, isAuthenticated, router])\n\n  // if (isLoading) {\n  //   return <Loading />\n  // }\n\n  // if (!isAuthenticated) {\n  //   return null // Will redirect to signin\n  // }\n\n  return <APITestingSuite />\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAEA;AANA;;;;;AAQe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,UAAO,AAAD;IACnD,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,6CAA6C;IAC7C,oBAAoB;IACpB,0CAA0C;IAC1C,kCAAkC;IAClC,MAAM;IACN,2CAA2C;IAE3C,mBAAmB;IACnB,uBAAuB;IACvB,IAAI;IAEJ,0BAA0B;IAC1B,2CAA2C;IAC3C,IAAI;IAEJ,qBAAO,8OAAC,+JAAA,CAAA,kBAAe;;;;;AACzB", "debugId": null}}]}