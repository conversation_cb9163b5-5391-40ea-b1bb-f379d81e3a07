{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 154, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/FlowUi/src/lib/supabase-server.ts"], "sourcesContent": ["import { createServerClient } from '@supabase/ssr'\nimport { cookies } from 'next/headers'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n\n// Server-side Supabase client for Server Components\nexport const createServerComponentClient = async () => {\n  const cookieStore = await cookies()\n  \n  return createServerClient(supabaseUrl, supabaseAnonKey, {\n    cookies: {\n      getAll() {\n        return cookieStore.getAll()\n      },\n      setAll(cookiesToSet) {\n        try {\n          cookiesToSet.forEach(({ name, value, options }) =>\n            cookieStore.set(name, value, options)\n          )\n        } catch {\n          // The `setAll` method was called from a Server Component.\n          // This can be ignored if you have middleware refreshing\n          // user sessions.\n        }\n      },\n    },\n  })\n}\n\n// Server-side Supabase client for Route Handlers\nexport const createRouteHandlerClient = (request: Request) => {\n  const response = new Response()\n  \n  return createServerClient(supabaseUrl, supabase<PERSON>non<PERSON>ey, {\n    cookies: {\n      getAll() {\n        const cookieHeader = request.headers.get('cookie')\n        if (!cookieHeader) return []\n        \n        return cookieHeader.split(';').map(cookie => {\n          const [name, value] = cookie.trim().split('=')\n          return { name, value }\n        })\n      },\n      setAll(cookiesToSet) {\n        cookiesToSet.forEach(({ name, value, options }) => {\n          response.headers.append('Set-Cookie', `${name}=${value}; ${Object.entries(options || {}).map(([k, v]) => `${k}=${v}`).join('; ')}`)\n        })\n      },\n    },\n  })\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AACA;;;AAEA,MAAM;AACN,MAAM;AAGC,MAAM,8BAA8B;IACzC,MAAM,cAAc,MAAM,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IAEhC,OAAO,CAAA,GAAA,2KAAA,CAAA,qBAAkB,AAAD,EAAE,aAAa,iBAAiB;QACtD,SAAS;YACP;gBACE,OAAO,YAAY,MAAM;YAC3B;YACA,QAAO,YAAY;gBACjB,IAAI;oBACF,aAAa,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,GAC5C,YAAY,GAAG,CAAC,MAAM,OAAO;gBAEjC,EAAE,OAAM;gBACN,0DAA0D;gBAC1D,wDAAwD;gBACxD,iBAAiB;gBACnB;YACF;QACF;IACF;AACF;AAGO,MAAM,2BAA2B,CAAC;IACvC,MAAM,WAAW,IAAI;IAErB,OAAO,CAAA,GAAA,2KAAA,CAAA,qBAAkB,AAAD,EAAE,aAAa,iBAAiB;QACtD,SAAS;YACP;gBACE,MAAM,eAAe,QAAQ,OAAO,CAAC,GAAG,CAAC;gBACzC,IAAI,CAAC,cAAc,OAAO,EAAE;gBAE5B,OAAO,aAAa,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA;oBACjC,MAAM,CAAC,MAAM,MAAM,GAAG,OAAO,IAAI,GAAG,KAAK,CAAC;oBAC1C,OAAO;wBAAE;wBAAM;oBAAM;gBACvB;YACF;YACA,QAAO,YAAY;gBACjB,aAAa,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE;oBAC5C,SAAS,OAAO,CAAC,MAAM,CAAC,cAAc,GAAG,KAAK,CAAC,EAAE,MAAM,EAAE,EAAE,OAAO,OAAO,CAAC,WAAW,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,GAAK,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,IAAI,CAAC,OAAO;gBACpI;YACF;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 211, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Developer/FlowUi/src/app/api/projects/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { createRouteHandlerClient } from '@/lib/supabase-server'\n\nexport async function GET(request: NextRequest) {\n  try {\n    const supabase = createRouteHandlerClient(request)\n    \n    // Get the current user\n    const { data: { user }, error: authError } = await supabase.auth.getUser()\n    \n    if (authError || !user) {\n      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })\n    }\n\n    // Get user's organizations\n    const { data: orgMembers, error: orgError } = await supabase\n      .from('organization_members')\n      .select('org_id')\n      .eq('user_id', user.id)\n\n    if (orgError) {\n      console.error('Error fetching organizations:', orgError)\n      return NextResponse.json({ error: 'Failed to fetch organizations' }, { status: 500 })\n    }\n\n    if (!orgMembers || orgMembers.length === 0) {\n      console.log('No organizations found for user, returning empty projects list')\n      return NextResponse.json({ projects: [] })\n    }\n\n    const orgIds = orgMembers.map(member => member.org_id)\n\n    // Get projects from user's organizations\n    const { data: projects, error: projectsError } = await supabase\n      .from('projects')\n      .select(`\n        id,\n        name,\n        description,\n        webhook_url,\n        config,\n        is_published,\n        public_url,\n        created_at,\n        updated_at,\n        ui_components (\n          id,\n          type,\n          props,\n          position,\n          size,\n          parent_id,\n          created_at,\n          updated_at\n        )\n      `)\n      .in('org_id', orgIds)\n      .order('updated_at', { ascending: false })\n\n    if (projectsError) {\n      return NextResponse.json({ error: 'Failed to fetch projects' }, { status: 500 })\n    }\n\n    // Transform the data to match our frontend interface\n    const transformedProjects = projects.map(project => ({\n      id: project.id,\n      name: project.name,\n      description: project.description,\n      webhook_url: project.webhook_url,\n      is_published: project.is_published,\n      public_url: project.public_url,\n      created_at: project.created_at,\n      updated_at: project.updated_at,\n      components: project.ui_components.map(component => ({\n        id: component.id,\n        project_id: project.id,\n        type: component.type,\n        props: component.props,\n        position: component.position,\n        size: component.size,\n        parent_id: component.parent_id,\n        created_at: component.created_at,\n        updated_at: component.updated_at,\n      }))\n    }))\n\n    return NextResponse.json({ projects: transformedProjects })\n  } catch (error) {\n    console.error('Error fetching projects:', error)\n    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })\n  }\n}\n\nexport async function POST(request: NextRequest) {\n  try {\n    const supabase = createRouteHandlerClient(request)\n    \n    // Get the current user\n    const { data: { user }, error: authError } = await supabase.auth.getUser()\n    \n    if (authError || !user) {\n      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })\n    }\n\n    const body = await request.json()\n    const { name, description, webhook_url, components = [] } = body\n\n    if (!name) {\n      return NextResponse.json({ error: 'Project name is required' }, { status: 400 })\n    }\n\n    // Get user's default organization (first one they're a member of)\n    let { data: orgMembers, error: orgError } = await supabase\n      .from('organization_members')\n      .select('org_id')\n      .eq('user_id', user.id)\n      .limit(1)\n\n    let orgId: string\n\n    if (orgError || !orgMembers.length) {\n      // Create a default organization for the user if none exists\n      console.log('No organization found for user, creating default organization')\n\n      const { data: newOrg, error: createOrgError } = await supabase\n        .from('organizations')\n        .insert({\n          name: `${user.email}'s Organization`,\n          owner_id: user.id\n        })\n        .select()\n        .single()\n\n      if (createOrgError) {\n        console.error('Error creating organization:', createOrgError)\n        return NextResponse.json({ error: 'Failed to create organization' }, { status: 500 })\n      }\n\n      // Add user as owner of the organization\n      const { error: memberError } = await supabase\n        .from('organization_members')\n        .insert({\n          org_id: newOrg.id,\n          user_id: user.id,\n          role: 'owner'\n        })\n\n      if (memberError) {\n        console.error('Error creating organization membership:', memberError)\n        return NextResponse.json({ error: 'Failed to create organization membership' }, { status: 500 })\n      }\n\n      orgId = newOrg.id\n    } else {\n      orgId = orgMembers[0].org_id\n    }\n\n    // Create the project\n    const { data: project, error: projectError } = await supabase\n      .from('projects')\n      .insert({\n        org_id: orgId,\n        name,\n        description,\n        webhook_url,\n        config: {}\n      })\n      .select()\n      .single()\n\n    if (projectError) {\n      console.error('Error creating project:', projectError)\n      return NextResponse.json({ error: 'Failed to create project' }, { status: 500 })\n    }\n\n    // Create components if provided\n    let createdComponents = []\n    if (components.length > 0) {\n      const componentsToInsert = components.map(component => ({\n        project_id: project.id,\n        type: component.type,\n        props: component.props,\n        position: component.position,\n        size: component.size,\n        parent_id: component.parent_id || null\n      }))\n\n      const { data: insertedComponents, error: componentsError } = await supabase\n        .from('ui_components')\n        .insert(componentsToInsert)\n        .select()\n\n      if (componentsError) {\n        console.error('Error creating components:', componentsError)\n        // Don't fail the entire request, just log the error\n      } else {\n        createdComponents = insertedComponents\n      }\n    }\n\n    const result = {\n      id: project.id,\n      name: project.name,\n      description: project.description,\n      webhook_url: project.webhook_url,\n      is_published: project.is_published,\n      public_url: project.public_url,\n      created_at: project.created_at,\n      updated_at: project.updated_at,\n      components: createdComponents.map(component => ({\n        id: component.id,\n        project_id: component.project_id,\n        type: component.type,\n        props: component.props,\n        position: component.position,\n        size: component.size,\n        parent_id: component.parent_id,\n        created_at: component.created_at,\n        updated_at: component.updated_at,\n      }))\n    }\n\n    return NextResponse.json({ project: result }, { status: 201 })\n  } catch (error) {\n    console.error('Error creating project:', error)\n    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,2BAAwB,AAAD,EAAE;QAE1C,uBAAuB;QACvB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QAExE,IAAI,aAAa,CAAC,MAAM;YACtB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAe,GAAG;gBAAE,QAAQ;YAAI;QACpE;QAEA,2BAA2B;QAC3B,MAAM,EAAE,MAAM,UAAU,EAAE,OAAO,QAAQ,EAAE,GAAG,MAAM,SACjD,IAAI,CAAC,wBACL,MAAM,CAAC,UACP,EAAE,CAAC,WAAW,KAAK,EAAE;QAExB,IAAI,UAAU;YACZ,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAgC,GAAG;gBAAE,QAAQ;YAAI;QACrF;QAEA,IAAI,CAAC,cAAc,WAAW,MAAM,KAAK,GAAG;YAC1C,QAAQ,GAAG,CAAC;YACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,UAAU,EAAE;YAAC;QAC1C;QAEA,MAAM,SAAS,WAAW,GAAG,CAAC,CAAA,SAAU,OAAO,MAAM;QAErD,yCAAyC;QACzC,MAAM,EAAE,MAAM,QAAQ,EAAE,OAAO,aAAa,EAAE,GAAG,MAAM,SACpD,IAAI,CAAC,YACL,MAAM,CAAC,CAAC;;;;;;;;;;;;;;;;;;;;MAoBT,CAAC,EACA,EAAE,CAAC,UAAU,QACb,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,eAAe;YACjB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAA2B,GAAG;gBAAE,QAAQ;YAAI;QAChF;QAEA,qDAAqD;QACrD,MAAM,sBAAsB,SAAS,GAAG,CAAC,CAAA,UAAW,CAAC;gBACnD,IAAI,QAAQ,EAAE;gBACd,MAAM,QAAQ,IAAI;gBAClB,aAAa,QAAQ,WAAW;gBAChC,aAAa,QAAQ,WAAW;gBAChC,cAAc,QAAQ,YAAY;gBAClC,YAAY,QAAQ,UAAU;gBAC9B,YAAY,QAAQ,UAAU;gBAC9B,YAAY,QAAQ,UAAU;gBAC9B,YAAY,QAAQ,aAAa,CAAC,GAAG,CAAC,CAAA,YAAa,CAAC;wBAClD,IAAI,UAAU,EAAE;wBAChB,YAAY,QAAQ,EAAE;wBACtB,MAAM,UAAU,IAAI;wBACpB,OAAO,UAAU,KAAK;wBACtB,UAAU,UAAU,QAAQ;wBAC5B,MAAM,UAAU,IAAI;wBACpB,WAAW,UAAU,SAAS;wBAC9B,YAAY,UAAU,UAAU;wBAChC,YAAY,UAAU,UAAU;oBAClC,CAAC;YACH,CAAC;QAED,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,UAAU;QAAoB;IAC3D,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAwB,GAAG;YAAE,QAAQ;QAAI;IAC7E;AACF;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,2BAAwB,AAAD,EAAE;QAE1C,uBAAuB;QACvB,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QAExE,IAAI,aAAa,CAAC,MAAM;YACtB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAe,GAAG;gBAAE,QAAQ;YAAI;QACpE;QAEA,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,IAAI,EAAE,WAAW,EAAE,WAAW,EAAE,aAAa,EAAE,EAAE,GAAG;QAE5D,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAA2B,GAAG;gBAAE,QAAQ;YAAI;QAChF;QAEA,kEAAkE;QAClE,IAAI,EAAE,MAAM,UAAU,EAAE,OAAO,QAAQ,EAAE,GAAG,MAAM,SAC/C,IAAI,CAAC,wBACL,MAAM,CAAC,UACP,EAAE,CAAC,WAAW,KAAK,EAAE,EACrB,KAAK,CAAC;QAET,IAAI;QAEJ,IAAI,YAAY,CAAC,WAAW,MAAM,EAAE;YAClC,4DAA4D;YAC5D,QAAQ,GAAG,CAAC;YAEZ,MAAM,EAAE,MAAM,MAAM,EAAE,OAAO,cAAc,EAAE,GAAG,MAAM,SACnD,IAAI,CAAC,iBACL,MAAM,CAAC;gBACN,MAAM,GAAG,KAAK,KAAK,CAAC,eAAe,CAAC;gBACpC,UAAU,KAAK,EAAE;YACnB,GACC,MAAM,GACN,MAAM;YAET,IAAI,gBAAgB;gBAClB,QAAQ,KAAK,CAAC,gCAAgC;gBAC9C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBAAE,OAAO;gBAAgC,GAAG;oBAAE,QAAQ;gBAAI;YACrF;YAEA,wCAAwC;YACxC,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,SAClC,IAAI,CAAC,wBACL,MAAM,CAAC;gBACN,QAAQ,OAAO,EAAE;gBACjB,SAAS,KAAK,EAAE;gBAChB,MAAM;YACR;YAEF,IAAI,aAAa;gBACf,QAAQ,KAAK,CAAC,2CAA2C;gBACzD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBAAE,OAAO;gBAA2C,GAAG;oBAAE,QAAQ;gBAAI;YAChG;YAEA,QAAQ,OAAO,EAAE;QACnB,OAAO;YACL,QAAQ,UAAU,CAAC,EAAE,CAAC,MAAM;QAC9B;QAEA,qBAAqB;QACrB,MAAM,EAAE,MAAM,OAAO,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,SAClD,IAAI,CAAC,YACL,MAAM,CAAC;YACN,QAAQ;YACR;YACA;YACA;YACA,QAAQ,CAAC;QACX,GACC,MAAM,GACN,MAAM;QAET,IAAI,cAAc;YAChB,QAAQ,KAAK,CAAC,2BAA2B;YACzC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAA2B,GAAG;gBAAE,QAAQ;YAAI;QAChF;QAEA,gCAAgC;QAChC,IAAI,oBAAoB,EAAE;QAC1B,IAAI,WAAW,MAAM,GAAG,GAAG;YACzB,MAAM,qBAAqB,WAAW,GAAG,CAAC,CAAA,YAAa,CAAC;oBACtD,YAAY,QAAQ,EAAE;oBACtB,MAAM,UAAU,IAAI;oBACpB,OAAO,UAAU,KAAK;oBACtB,UAAU,UAAU,QAAQ;oBAC5B,MAAM,UAAU,IAAI;oBACpB,WAAW,UAAU,SAAS,IAAI;gBACpC,CAAC;YAED,MAAM,EAAE,MAAM,kBAAkB,EAAE,OAAO,eAAe,EAAE,GAAG,MAAM,SAChE,IAAI,CAAC,iBACL,MAAM,CAAC,oBACP,MAAM;YAET,IAAI,iBAAiB;gBACnB,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,oDAAoD;YACtD,OAAO;gBACL,oBAAoB;YACtB;QACF;QAEA,MAAM,SAAS;YACb,IAAI,QAAQ,EAAE;YACd,MAAM,QAAQ,IAAI;YAClB,aAAa,QAAQ,WAAW;YAChC,aAAa,QAAQ,WAAW;YAChC,cAAc,QAAQ,YAAY;YAClC,YAAY,QAAQ,UAAU;YAC9B,YAAY,QAAQ,UAAU;YAC9B,YAAY,QAAQ,UAAU;YAC9B,YAAY,kBAAkB,GAAG,CAAC,CAAA,YAAa,CAAC;oBAC9C,IAAI,UAAU,EAAE;oBAChB,YAAY,UAAU,UAAU;oBAChC,MAAM,UAAU,IAAI;oBACpB,OAAO,UAAU,KAAK;oBACtB,UAAU,UAAU,QAAQ;oBAC5B,MAAM,UAAU,IAAI;oBACpB,WAAW,UAAU,SAAS;oBAC9B,YAAY,UAAU,UAAU;oBAChC,YAAY,UAAU,UAAU;gBAClC,CAAC;QACH;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,SAAS;QAAO,GAAG;YAAE,QAAQ;QAAI;IAC9D,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAwB,GAAG;YAAE,QAAQ;QAAI;IAC7E;AACF", "debugId": null}}]}