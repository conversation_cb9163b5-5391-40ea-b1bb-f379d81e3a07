{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_6813fc78._.js", "server/edge/chunks/[root-of-the-server]__de110ef0._.js", "server/edge/chunks/edge-wrapper_895135be.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*){(\\\\.json)}?", "originalSource": "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "XBnWTXQTpZuckHnYdyfrhWX2ugBIK4p1ITOuQN7l/Wo=", "__NEXT_PREVIEW_MODE_ID": "41228f8950cb18fea4a1c95498f41815", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "396550617aa415db94d73ea78bc6b1e367041cedadd791e1da59bd48053c5901", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "f943d35b89f4d9857a644c86be6a1f7256fa6e48f533724667488338293aac83"}}}, "instrumentation": null, "functions": {}}