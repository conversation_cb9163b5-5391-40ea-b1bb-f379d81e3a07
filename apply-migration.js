const { createClient } = require('@supabase/supabase-js')
const fs = require('fs')
require('dotenv').config({ path: '.env.local' })

async function applyMigration() {
  const supabase = createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL,
    process.env.SUPABASE_SERVICE_ROLE_KEY
  )

  const migrationSQL = fs.readFileSync('./supabase/migrations/20240102000000_add_organization_members_insert_policy.sql', 'utf8')
  
  console.log('Applying migration...')
  console.log('SQL:', migrationSQL)
  
  const { data, error } = await supabase.rpc('exec_sql', { sql: migrationSQL })
  
  if (error) {
    console.error('Migration failed:', error)
  } else {
    console.log('Migration applied successfully:', data)
  }
}

applyMigration().catch(console.error)
